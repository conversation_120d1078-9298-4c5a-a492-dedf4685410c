"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { MapPin, Phone, Mail, Globe, Building, Eye, EyeOff, Clock } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface BusinessInfo {
  id: string
  business_name: string
  address: string
  phone: string
  email: string
  website: string | null
  description: string | null
}

interface BusinessVisibilitySettings {
  id: string
  show_address: boolean
  show_phone: boolean
  show_email: boolean
  show_website: boolean
  show_hours: boolean
  show_contact_section: boolean
}

export function AdminBusinessInfo() {
  const [businessInfo, setBusinessInfo] = useState<BusinessInfo | null>(null)
  const [visibilitySettings, setVisibilitySettings] = useState<BusinessVisibilitySettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    business_name: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    description: ''
  })
  const [visibilityData, setVisibilityData] = useState({
    show_address: true,
    show_phone: true,
    show_email: true,
    show_website: true,
    show_hours: true,
    show_contact_section: true
  })

  useEffect(() => {
    fetchBusinessInfo()
    fetchVisibilitySettings()
  }, [])

  const fetchBusinessInfo = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('business_info')
        .select('*')
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error fetching business info:', error)
        toast.error('Failed to load business information')
        return
      }

      if (data) {
        setBusinessInfo(data)
        setFormData({
          business_name: data.business_name,
          address: data.address,
          phone: data.phone,
          email: data.email,
          website: data.website || '',
          description: data.description || ''
        })
      }
    } catch (error) {
      console.error('Error fetching business info:', error)
      toast.error('Failed to load business information')
    } finally {
      setLoading(false)
    }
  }

  const fetchVisibilitySettings = async () => {
    try {
      const { data, error } = await supabase
        .from('business_visibility_settings')
        .select('*')
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching visibility settings:', error)
        return
      }

      if (data) {
        setVisibilitySettings(data)
        setVisibilityData({
          show_address: data.show_address,
          show_phone: data.show_phone,
          show_email: data.show_email,
          show_website: data.show_website,
          show_hours: data.show_hours,
          show_contact_section: data.show_contact_section
        })
      }
    } catch (error) {
      console.error('Error fetching visibility settings:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.business_name.trim() || !formData.address.trim() || !formData.phone.trim() || !formData.email.trim()) {
      toast.error('Please fill in all required fields')
      return
    }

    setSaving(true)
    try {
      const businessData = {
        business_name: formData.business_name.trim(),
        address: formData.address.trim(),
        phone: formData.phone.trim(),
        email: formData.email.trim(),
        website: formData.website.trim() || null,
        description: formData.description.trim() || null
      }

      if (businessInfo) {
        // Update existing business info
        const { error } = await supabase
          .from('business_info')
          .update(businessData)
          .eq('id', businessInfo.id)

        if (error) {
          console.error('Error updating business info:', error)
          toast.error('Failed to update business information')
          return
        }

        toast.success('Business information updated successfully!')
      } else {
        // Create new business info
        const { data, error } = await supabase
          .from('business_info')
          .insert(businessData)
          .select()
          .single()

        if (error) {
          console.error('Error creating business info:', error)
          toast.error('Failed to save business information')
          return
        }

        setBusinessInfo(data)
        toast.success('Business information saved successfully!')
      }

      fetchBusinessInfo()
    } catch (error) {
      console.error('Error saving business info:', error)
      toast.error('Failed to save business information')
    } finally {
      setSaving(false)
    }
  }

  const handleSaveVisibility = async () => {
    setSaving(true)
    try {
      const visibilityPayload = {
        show_address: visibilityData.show_address,
        show_phone: visibilityData.show_phone,
        show_email: visibilityData.show_email,
        show_website: visibilityData.show_website,
        show_hours: visibilityData.show_hours,
        show_contact_section: visibilityData.show_contact_section
      }

      if (visibilitySettings) {
        const { error } = await supabase
          .from('business_visibility_settings')
          .update(visibilityPayload)
          .eq('id', visibilitySettings.id)

        if (error) {
          console.error('Error updating visibility settings:', error)
          toast.error('Failed to update visibility settings')
          return
        }
      } else {
        const { data, error } = await supabase
          .from('business_visibility_settings')
          .insert(visibilityPayload)
          .select()
          .single()

        if (error) {
          console.error('Error creating visibility settings:', error)
          toast.error('Failed to save visibility settings')
          return
        }

        setVisibilitySettings(data)
      }

      toast.success('Visibility settings saved successfully!')
      fetchVisibilitySettings()
    } catch (error) {
      console.error('Error saving visibility settings:', error)
      toast.error('Failed to save visibility settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="h-96 bg-gray-200 rounded animate-pulse" />
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 max-w-none w-full">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Business Information</h2>
        <p className="text-gray-600 dark:text-gray-400">Manage your business contact details and description</p>
      </div>

      {/* Visibility Settings */}
      <Card className="border-pink-200">
        <CardHeader>
          <CardTitle className="text-pink-800 flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Contact Section Visibility
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-pink-100 rounded-lg">
            <div>
              <Label className="text-pink-800 font-medium">Show Contact Section</Label>
              <p className="text-sm text-gray-500">Display the entire contact information section on homepage</p>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={visibilityData.show_contact_section}
                onCheckedChange={(checked) => setVisibilityData({ ...visibilityData, show_contact_section: checked })}
              />
              {visibilityData.show_contact_section ? (
                <Eye className="h-4 w-4 text-green-600" />
              ) : (
                <EyeOff className="h-4 w-4 text-gray-400" />
              )}
            </div>
          </div>

          <div className="flex items-center justify-between p-4 border border-pink-100 rounded-lg">
            <div>
              <Label className="text-pink-800 font-medium">Show Business Hours</Label>
              <p className="text-sm text-gray-500">Display operating hours in contact section</p>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                checked={visibilityData.show_hours}
                onCheckedChange={(checked) => setVisibilityData({ ...visibilityData, show_hours: checked })}
              />
              {visibilityData.show_hours ? (
                <Eye className="h-4 w-4 text-green-600" />
              ) : (
                <EyeOff className="h-4 w-4 text-gray-400" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Business Info Form */}
      <Card className="border-pink-200">
        <CardHeader>
          <CardTitle className="text-pink-800 flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Business Details & Field Visibility
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="business_name" className="text-pink-800">Business Name *</Label>
              <Input
                id="business_name"
                value={formData.business_name}
                onChange={(e) => setFormData({ ...formData, business_name: e.target.value })}
                className="border-pink-200 focus:border-pink-500"
                placeholder="Your business name"
                required
              />
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="address" className="text-pink-800">Address</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={visibilityData.show_address}
                    onCheckedChange={(checked) => setVisibilityData({ ...visibilityData, show_address: checked })}
                  />
                  {visibilityData.show_address ? (
                    <Eye className="h-4 w-4 text-green-600" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="text-xs text-gray-500">
                    {visibilityData.show_address ? 'Visible' : 'Hidden'}
                  </span>
                </div>
              </div>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  className="pl-10 border-pink-200 focus:border-pink-500"
                  placeholder="123 Main Street, City, State 12345"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="phone" className="text-pink-800">Phone Number</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibilityData.show_phone}
                      onCheckedChange={(checked) => setVisibilityData({ ...visibilityData, show_phone: checked })}
                    />
                    {visibilityData.show_phone ? (
                      <Eye className="h-4 w-4 text-green-600" />
                    ) : (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="text-xs text-gray-500">
                      {visibilityData.show_phone ? 'Visible' : 'Hidden'}
                    </span>
                  </div>
                </div>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="pl-10 border-pink-200 focus:border-pink-500"
                    placeholder="(*************"
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="email" className="text-pink-800">Email Address</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibilityData.show_email}
                      onCheckedChange={(checked) => setVisibilityData({ ...visibilityData, show_email: checked })}
                    />
                    {visibilityData.show_email ? (
                      <Eye className="h-4 w-4 text-green-600" />
                    ) : (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="text-xs text-gray-500">
                      {visibilityData.show_email ? 'Visible' : 'Hidden'}
                    </span>
                  </div>
                </div>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="pl-10 border-pink-200 focus:border-pink-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="website" className="text-pink-800">Website (Optional)</Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={visibilityData.show_website}
                    onCheckedChange={(checked) => setVisibilityData({ ...visibilityData, show_website: checked })}
                  />
                  {visibilityData.show_website ? (
                    <Eye className="h-4 w-4 text-green-600" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="text-xs text-gray-500">
                    {visibilityData.show_website ? 'Visible' : 'Hidden'}
                  </span>
                </div>
              </div>
              <div className="relative">
                <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                  className="pl-10 border-pink-200 focus:border-pink-500"
                  placeholder="https://yourbusiness.com"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description" className="text-pink-800">Business Description (Optional)</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="border-pink-200 focus:border-pink-500 min-h-[120px]"
                placeholder="Describe your business, services, and what makes you special..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                type="submit"
                disabled={saving}
                className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
              >
                {saving ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </div>
                ) : (
                  businessInfo ? 'Update Business Info' : 'Save Business Info'
                )}
              </Button>

              <Button
                type="button"
                onClick={handleSaveVisibility}
                disabled={saving}
                variant="outline"
                className="border-pink-300 text-pink-700 hover:bg-pink-50"
              >
                Save Visibility Settings
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Preview Card */}
      {businessInfo && (
        <Card className="border-pink-200 bg-gradient-to-r from-pink-50 to-amber-50">
          <CardHeader>
            <CardTitle className="text-pink-800">Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <h3 className="text-xl font-bold text-pink-800">{formData.business_name}</h3>
              
              <div className="flex items-start space-x-2 text-gray-700">
                <MapPin className="h-4 w-4 text-pink-600 mt-0.5 flex-shrink-0" />
                <span>{formData.address}</span>
              </div>
              
              <div className="flex items-center space-x-2 text-gray-700">
                <Phone className="h-4 w-4 text-pink-600" />
                <span>{formData.phone}</span>
              </div>
              
              <div className="flex items-center space-x-2 text-gray-700">
                <Mail className="h-4 w-4 text-pink-600" />
                <span>{formData.email}</span>
              </div>
              
              {formData.website && (
                <div className="flex items-center space-x-2 text-gray-700">
                  <Globe className="h-4 w-4 text-pink-600" />
                  <a href={formData.website} target="_blank" rel="noopener noreferrer" className="text-pink-600 hover:underline">
                    {formData.website}
                  </a>
                </div>
              )}
              
              {formData.description && (
                <div className="mt-4">
                  <p className="text-gray-700 leading-relaxed">{formData.description}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
