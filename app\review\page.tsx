"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { AppHeader } from "@/components/ui/app-header"
import { StarRating } from "@/components/ui/star-rating"
import { TipSelector } from "@/components/ui/tip-selector"
import { Camera, Heart, Send, CheckCircle } from "lucide-react"
import { useRouter } from "next/navigation"

// Mock appointment data - in real app this would come from props or API
const appointmentData = {
  service: "Gel Manicure with Nail Art",
  artist: "<PERSON> Michelle",
  date: "Today, 2:30 PM",
  price: 75,
  duration: "90 min",
}

export default function ReviewPage() {
  const router = useRouter()
  const [step, setStep] = useState(1) // 1: Review, 2: Tip, 3: Confirmation
  const [rating, setRating] = useState(0)
  const [review, setReview] = useState("")
  const [tip, setTip] = useState(0)
  const [photos, setPhotos] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      // In a real app, you'd upload these files to a server
      const newPhotos = Array.from(files).map((file) => URL.createObjectURL(file))
      setPhotos([...photos, ...newPhotos])
    }
  }

  const handleSubmitReview = async () => {
    setIsSubmitting(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setIsSubmitting(false)
    setStep(3)
  }

  const canProceedToTip = rating > 0 && review.trim().length > 0

  if (step === 3) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <AppHeader title="Thank You!" />

        <div className="p-4 flex items-center justify-center min-h-[70vh]">
          <Card className="border-0 shadow-lg text-center max-w-sm">
            <CardContent className="p-8">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Review Submitted!</h2>
              <p className="text-gray-600 mb-6">
                Thank you for your feedback and {tip > 0 ? "generous tip" : "review"}! We appreciate your business and
                look forward to seeing you again.
              </p>

              {tip > 0 && (
                <div className="bg-pink-50 p-4 rounded-lg mb-6 border border-pink-200">
                  <p className="text-pink-700 font-semibold">
                    Tip of ${tip.toFixed(2)} sent to {appointmentData.artist}
                  </p>
                </div>
              )}

              <div className="space-y-3">
                <Button
                  onClick={() => router.push("/booking")}
                  className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  Book Next Appointment
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/")}
                  className="w-full border-pink-200 text-pink-700"
                >
                  Back to Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <AppHeader title={step === 1 ? "Rate Your Experience" : "Add a Tip"} showBack />

      <div className="p-4 space-y-6">
        {/* Appointment Summary */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-amber-500 rounded-full flex items-center justify-center mr-4">
                <Heart className="h-6 w-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">{appointmentData.service}</h3>
                <p className="text-sm text-gray-600">with {appointmentData.artist}</p>
                <p className="text-sm text-gray-500">
                  {appointmentData.date} • {appointmentData.duration}
                </p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-pink-600">${appointmentData.price}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {step === 1 && (
          <>
            {/* Rating Section */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-6 text-center">
                <h2 className="text-xl font-bold text-gray-900 mb-4">How was your experience?</h2>
                <StarRating rating={rating} onRatingChange={setRating} size="lg" />
                {rating > 0 && (
                  <p className="text-pink-600 mt-2 font-medium">
                    {rating === 5 && "Excellent! ⭐"}
                    {rating === 4 && "Great! 😊"}
                    {rating === 3 && "Good! 👍"}
                    {rating === 2 && "Okay 👌"}
                    {rating === 1 && "Needs improvement 📝"}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Review Text */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-4 space-y-4">
                <Label htmlFor="review" className="text-base font-semibold text-gray-900">
                  Tell us about your experience
                </Label>
                <Textarea
                  id="review"
                  placeholder="Share your thoughts about the service, nail art, atmosphere, or anything else..."
                  value={review}
                  onChange={(e) => setReview(e.target.value)}
                  className="min-h-[120px] border-pink-200 focus:border-pink-500 resize-none"
                />
                <p className="text-sm text-gray-500">{review.length}/500 characters</p>
              </CardContent>
            </Card>

            {/* Photo Upload */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-4 space-y-4">
                <Label className="text-base font-semibold text-gray-900">Show off your nails! (Optional)</Label>

                <div className="grid grid-cols-3 gap-3">
                  {photos.map((photo, index) => (
                    <img
                      key={index}
                      src={photo || "/placeholder.svg"}
                      alt={`Nail photo ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg border-2 border-pink-200"
                    />
                  ))}

                  {photos.length < 3 && (
                    <label className="w-full h-24 border-2 border-dashed border-pink-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:bg-pink-50 transition-colors">
                      <Camera className="h-6 w-6 text-pink-400 mb-1" />
                      <span className="text-xs text-pink-600">Add Photo</span>
                      <input type="file" accept="image/*" multiple className="hidden" onChange={handlePhotoUpload} />
                    </label>
                  )}
                </div>

                <p className="text-sm text-gray-500">Add up to 3 photos of your beautiful nails!</p>
              </CardContent>
            </Card>

            {/* Next Button */}
            <Button
              onClick={() => setStep(2)}
              disabled={!canProceedToTip}
              className="w-full py-3 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600 disabled:opacity-50"
            >
              Continue to Tip
            </Button>
          </>
        )}

        {step === 2 && (
          <>
            {/* Review Summary */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-gray-700">Your rating:</span>
                  <StarRating rating={rating} onRatingChange={() => {}} readonly size="sm" />
                </div>
                <p className="text-sm text-gray-600 italic">
                  "{review.slice(0, 100)}
                  {review.length > 100 ? "..." : ""}"
                </p>
              </CardContent>
            </Card>

            {/* Tip Section */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-4">
                <TipSelector servicePrice={appointmentData.price} onTipChange={setTip} />
              </CardContent>
            </Card>

            {/* Submit Button */}
            <Button
              onClick={handleSubmitReview}
              disabled={isSubmitting}
              className="w-full py-3 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600 disabled:opacity-50"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Submitting...
                </div>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Submit Review {tip > 0 && "& Tip"}
                </>
              )}
            </Button>
          </>
        )}
      </div>
    </div>
  )
}
