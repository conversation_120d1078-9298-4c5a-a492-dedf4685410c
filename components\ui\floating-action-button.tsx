import { Calendar } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function FloatingActionButton() {
  return (
    <Link href="/booking" className="fixed bottom-20 right-4 z-50">
      <Button
        size="lg"
        className="h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600 hover:shadow-xl transition-all"
      >
        <Calendar className="h-6 w-6" />
      </Button>
    </Link>
  )
}
