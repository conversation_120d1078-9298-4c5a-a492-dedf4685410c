import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Calendar, ChevronRight, User, Gift } from "lucide-react"
import { AppHeader } from "@/components/ui/app-header"
import { FloatingActionButton } from "@/components/ui/floating-action-button"
import { MyWorkGallery } from "@/components/my-work-gallery"
import { ImageUpload } from "@/components/image-upload"
import { HomepageStats } from "@/components/homepage-stats"
import { CustomerReviews } from "@/components/customer-reviews"
import { FeaturedServices } from "@/components/featured-services"
import { DynamicContactInfo } from "@/components/dynamic-contact-info"



export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20">
      <AppHeader title="Nails by Lingg" showNotifications showLogo />

      {/* Hero Card */}
      <div className="p-4">
        <Card className="relative overflow-hidden bg-gradient-to-br from-pink-500 to-amber-500 border-0">
          <CardContent className="p-6 text-white">
            <div className="relative z-10">
              <h1 className="text-3xl font-bold mb-2" style={{ fontFamily: "Dancing Script, cursive" }}>
                Welcome Back!
              </h1>
              <p className="text-pink-100 mb-6">Ready for your next luxury nail experience?</p>
              <Button asChild className="bg-white text-pink-600 hover:bg-pink-50 rounded-full font-semibold">
                <Link href="/booking">
                  <Calendar className="mr-2 h-4 w-4" />
                  Book Appointment
                </Link>
              </Button>
            </div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16" />
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -ml-12 -mb-12" />
          </CardContent>
        </Card>
      </div>

      {/* Quick Stats */}
      <div className="px-4 mb-6">
        <HomepageStats />
      </div>

      {/* Quick Actions */}
      <div className="px-4 mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-3">
          <Link href="/profile">
            <Card className="border-0 shadow-sm hover:shadow-md transition-shadow dark:bg-gray-800">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center mx-auto mb-3">
                  <User className="h-6 w-6 text-pink-600 dark:text-pink-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">My Profile</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">View photos & rewards</p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/referrals">
            <Card className="border-0 shadow-sm hover:shadow-md transition-shadow dark:bg-gray-800">
              <CardContent className="p-4 text-center">
                <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Gift className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">Refer Friends</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">Earn $10 per referral</p>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>

      {/* Popular Services */}
      <div className="px-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Popular Services</h2>
          <Button variant="ghost" size="sm" asChild className="text-pink-600 dark:text-pink-400">
            <Link href="/services">
              View All
              <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>

        <FeaturedServices limit={4} showPopular={true} />
      </div>

      {/* My Work Gallery */}
      <div className="px-4 mb-6">
        <MyWorkGallery limit={4} />
      </div>

      {/* Recent Reviews */}
      <div className="px-4 mb-6">
        <CustomerReviews limit={3} featuredOnly={true} />
      </div>

      {/* Contact Info */}
      <div className="px-4 mb-6">
        <DynamicContactInfo />
      </div>

      <FloatingActionButton />
    </div>
  )
}
