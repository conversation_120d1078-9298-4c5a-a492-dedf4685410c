import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { sendAnnouncementEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const { announcementId, title, content, subscribers } = await request.json()

    // Verify the request is from an admin
    const supabase = createServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile?.is_admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Validate input
    if (!announcementId || !title || !content || !Array.isArray(subscribers)) {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 })
    }

    if (subscribers.length === 0) {
      return NextResponse.json({ error: 'No subscribers provided' }, { status: 400 })
    }

    // Send the announcement email
    const result = await sendAnnouncementEmail(subscribers, title, content)

    if (!result.success) {
      console.error('Failed to send announcement email:', result.error)
      return NextResponse.json({ error: 'Failed to send announcement' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: `Announcement sent to ${subscribers.length} subscribers`,
      data: result.data 
    })

  } catch (error) {
    console.error('Send announcement error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
