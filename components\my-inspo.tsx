"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Heart, User, Calendar, ChevronRight, Bookmark } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/components/auth/auth-provider'
import { toast } from 'sonner'
import Link from 'next/link'

interface LikedImage {
  id: string
  image_id: string
  created_at: string
  nail_image: {
    id: string
    image_url: string
    likes_count: number
    is_featured: boolean
    user: {
      first_name: string
      last_name: string
    }
    appointment: {
      appointment_date: string
      services: { name: string }[]
    }
  }
}

interface MyInspoProps {
  showTitle?: boolean
  limit?: number
  showViewAll?: boolean
}

export function MyInspo({ showTitle = true, limit, showViewAll = true }: MyInspoProps) {
  const [likedImages, setLikedImages] = useState<LikedImage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user, isSignedIn } = useAuth()

  useEffect(() => {
    if (isSignedIn && user) {
      fetchLikedImages()
    } else {
      setLoading(false)
    }
  }, [isSignedIn, user, limit])

  const fetchLikedImages = async () => {
    if (!user) return

    setLoading(true)
    setError(null)
    try {
      let query = supabase
        .from('image_likes')
        .select(`
          id,
          image_id,
          created_at,
          nail_image:nail_images!image_likes_image_id_fkey (
            id,
            image_url,
            likes_count,
            is_featured,
            user:users!nail_images_user_id_fkey (
              first_name,
              last_name
            ),
            appointment:appointments!nail_images_appointment_id_fkey (
              appointment_date,
              services:service_ids (
                name
              )
            )
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (limit) {
        query = query.limit(limit)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching liked images:', error)
        setError('Failed to load inspiration')
        return
      }

      // Transform the data to handle the relationship arrays correctly
      const transformedData = (data || []).map(like => ({
        ...like,
        nail_image: {
          ...like.nail_image[0],
          user: Array.isArray(like.nail_image[0]?.user) ? like.nail_image[0].user[0] : like.nail_image[0]?.user,
          appointment: Array.isArray(like.nail_image[0]?.appointment) ? like.nail_image[0].appointment[0] : like.nail_image[0]?.appointment
        }
      }))
      setLikedImages(transformedData)
    } catch (error) {
      console.error('Error fetching liked images:', error)
      setError('Failed to load inspiration')
    } finally {
      setLoading(false)
    }
  }

  const handleUnlike = async (imageId: string, likeId: string) => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('image_likes')
        .delete()
        .eq('id', likeId)

      if (error) {
        console.error('Error unliking image:', error)
        toast.error('Failed to remove from inspiration')
        return
      }

      // Update likes count
      const { error: updateError } = await supabase.rpc('decrement_likes_count', {
        image_id: imageId
      })

      if (updateError) {
        console.error('Error updating likes count:', updateError)
      }

      // Remove from local state
      setLikedImages(prev => prev.filter(like => like.id !== likeId))
      toast.success('Removed from inspiration')
    } catch (error) {
      console.error('Error unliking image:', error)
      toast.error('Failed to remove from inspiration')
    }
  }

  if (!isSignedIn) {
    return (
      <div className="space-y-4">
        {showTitle && <h2 className="text-xl font-bold text-gray-900">My Inspo</h2>}
        <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <Bookmark className="h-12 w-12 text-pink-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 mb-2">Save Your Inspiration</h3>
            <p className="text-pink-600 mb-4">Sign in to like and save nail art that inspires you!</p>
            <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              Sign In to Save
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {showTitle && <h2 className="text-xl font-bold text-gray-900">My Inspo</h2>}
        <div className="grid grid-cols-2 gap-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="aspect-square bg-gray-200 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        {showTitle && <h2 className="text-xl font-bold text-gray-900">My Inspo</h2>}
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <Bookmark className="h-12 w-12 text-red-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Inspiration</h3>
            <p className="text-red-600 mb-4">{error}</p>
            <Button
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100"
              onClick={fetchLikedImages}
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (likedImages.length === 0) {
    return (
      <div className="space-y-4">
        {showTitle && <h2 className="text-xl font-bold text-gray-900">My Inspo</h2>}
        <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <Bookmark className="h-12 w-12 text-pink-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 mb-2">No Inspiration Yet</h3>
            <p className="text-pink-600 mb-4">Start liking photos to build your inspiration collection!</p>
            <Button variant="outline" className="border-pink-300 text-pink-700" asChild>
              <Link href="/">Browse Gallery</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {showTitle && (
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">My Inspo</h2>
          {showViewAll && likedImages.length > 0 && (
            <Button variant="ghost" size="sm" className="text-pink-600" asChild>
              <Link href="/profile?tab=inspo">
                View All
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          )}
        </div>
      )}

      <div className="grid grid-cols-2 gap-3">
        {likedImages.map((like) => (
          <Card key={like.id} className="border-0 shadow-sm overflow-hidden group">
            <div className="relative aspect-square">
              <img 
                src={like.nail_image.image_url} 
                alt="Nail art inspiration" 
                className="w-full h-full object-cover transition-transform group-hover:scale-105"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="absolute bottom-0 left-0 right-0 p-3">
                  <div className="flex items-center justify-between text-white text-sm">
                    <div className="flex items-center space-x-2">
                      <User className="h-3 w-3" />
                      <span>{like.nail_image.user.first_name} {like.nail_image.user.last_name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(like.nail_image.appointment.appointment_date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Featured badge */}
              {like.nail_image.is_featured && (
                <Badge className="absolute top-2 left-2 bg-amber-500 text-white">
                  Featured
                </Badge>
              )}

              {/* Unlike button */}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleUnlike(like.nail_image.id, like.id)}
                className="absolute top-2 right-2 bg-white/80 hover:bg-white text-pink-600 h-8 w-8 p-0"
              >
                <Heart className="h-4 w-4 fill-current" />
              </Button>
            </div>

            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-pink-600">
                  <Heart className="h-4 w-4" />
                  <span className="text-sm font-medium">{like.nail_image.likes_count}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {like.nail_image.appointment.services?.map(s => s.name).join(', ')}
                </div>
              </div>
              <div className="text-xs text-gray-400 mt-1">
                Saved {new Date(like.created_at).toLocaleDateString()}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
