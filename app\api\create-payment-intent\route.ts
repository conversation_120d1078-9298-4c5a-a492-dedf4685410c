import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { createPaymentIntent } from '@/lib/stripe'

export async function POST(request: NextRequest) {
  try {
    const { amount, currency = 'usd', metadata } = await request.json()

    // Verify the request is from an authenticated user
    const supabase = createServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Validate input
    if (!amount || amount <= 0) {
      return NextResponse.json({ error: 'Invalid amount' }, { status: 400 })
    }

    // Create payment intent
    const paymentIntent = await createPaymentIntent({
      amount,
      currency,
      metadata: {
        userId: user.id,
        userEmail: user.email!,
        ...metadata
      }
    })

    return NextResponse.json({
      clientSecret: paymentIntent.clientSecret,
      paymentIntentId: paymentIntent.paymentIntentId
    })

  } catch (error) {
    console.error('Payment intent creation error:', error)
    return NextResponse.json({ 
      error: 'Failed to create payment intent', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
