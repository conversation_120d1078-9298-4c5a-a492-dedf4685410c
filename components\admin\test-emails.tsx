"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Mail, Send, CheckCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

interface EmailTemplate {
  id: string
  name: string
  description: string
  requiresData?: boolean
  dataFields?: string[]
}

const emailTemplates: EmailTemplate[] = [
  {
    id: 'verification',
    name: 'Email Verification',
    description: 'Email verification template for new user registrations',
    requiresData: false
  },
  {
    id: 'magic-link',
    name: 'Magic Link Sign In',
    description: 'Secure sign-in link for passwordless authentication',
    requiresData: false
  },
  {
    id: 'password-reset',
    name: 'Password Reset',
    description: 'Password reset instructions and secure link',
    requiresData: false
  },
  {
    id: 'appointment-confirmation',
    name: 'Appointment Confirmation',
    description: 'Booking confirmation with appointment details',
    requiresData: true,
    dataFields: ['appointmentDate', 'appointmentTime', 'services', 'totalPrice']
  },
  {
    id: 'announcement',
    name: 'Announcement Email',
    description: 'Newsletter and announcement template',
    requiresData: true,
    dataFields: ['title', 'content']
  },
  {
    id: 'referral-invitation',
    name: 'Referral Invitation',
    description: 'Invite friends with special discount offer',
    requiresData: true,
    dataFields: ['senderName', 'referralCode']
  }
]

export function AdminTestEmails() {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [recipientEmail, setRecipientEmail] = useState('')
  const [templateData, setTemplateData] = useState<Record<string, string>>({})
  const [sending, setSending] = useState(false)
  const [lastSent, setLastSent] = useState<{ template: string; email: string; time: Date } | null>(null)

  const selectedTemplateObj = emailTemplates.find(t => t.id === selectedTemplate)

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId)
    setTemplateData({})
  }

  const handleDataChange = (field: string, value: string) => {
    setTemplateData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSendTestEmail = async () => {
    if (!selectedTemplate || !recipientEmail) {
      toast.error('Please select a template and enter recipient email')
      return
    }

    if (selectedTemplateObj?.requiresData) {
      const missingFields = selectedTemplateObj.dataFields?.filter(field => !templateData[field]?.trim()) || []
      if (missingFields.length > 0) {
        toast.error(`Please fill in all required fields: ${missingFields.join(', ')}`)
        return
      }
    }

    setSending(true)
    try {
      const response = await fetch('/api/send-test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId: selectedTemplate,
          recipientEmail,
          templateData
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send test email')
      }

      toast.success(`Test email sent successfully to ${recipientEmail}`)
      setLastSent({
        template: selectedTemplateObj?.name || selectedTemplate,
        email: recipientEmail,
        time: new Date()
      })
    } catch (error) {
      console.error('Error sending test email:', error)
      toast.error('Failed to send test email. Please try again.')
    } finally {
      setSending(false)
    }
  }

  return (
    <div className="p-6 space-y-6 max-w-none w-full">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Test Emails</h2>
        <p className="text-gray-600 dark:text-gray-400">Send test emails using your custom email templates</p>
      </div>

      {/* Last Sent Status */}
      {lastSent && (
        <Card className="border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-700">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-300">
                  Last sent: {lastSent.template} to {lastSent.email}
                </p>
                <p className="text-xs text-green-600 dark:text-green-400">
                  {lastSent.time.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Email Template Selection */}
      <Card className="border-pink-200 dark:border-pink-700">
        <CardHeader>
          <CardTitle className="text-pink-800 dark:text-pink-300 flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Email Template Selection
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="template" className="text-pink-800 dark:text-pink-300">
              Select Email Template *
            </Label>
            <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
              <SelectTrigger className="border-pink-200 dark:border-pink-700">
                <SelectValue placeholder="Choose an email template" />
              </SelectTrigger>
              <SelectContent>
                {emailTemplates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-sm text-gray-500">{template.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="email" className="text-pink-800 dark:text-pink-300">
              Recipient Email *
            </Label>
            <Input
              id="email"
              type="email"
              value={recipientEmail}
              onChange={(e) => setRecipientEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="border-pink-200 dark:border-pink-700"
              required
            />
          </div>
        </CardContent>
      </Card>

      {/* Template Data Fields */}
      {selectedTemplateObj?.requiresData && (
        <Card className="border-pink-200 dark:border-pink-700">
          <CardHeader>
            <CardTitle className="text-pink-800 dark:text-pink-300">Template Data</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {selectedTemplateObj.dataFields?.map((field) => (
              <div key={field}>
                <Label htmlFor={field} className="text-pink-800 dark:text-pink-300 capitalize">
                  {field.replace(/([A-Z])/g, ' $1').trim()} *
                </Label>
                <Input
                  id={field}
                  value={templateData[field] || ''}
                  onChange={(e) => handleDataChange(field, e.target.value)}
                  placeholder={`Enter ${field.replace(/([A-Z])/g, ' $1').trim().toLowerCase()}`}
                  className="border-pink-200 dark:border-pink-700"
                  required
                />
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Template Preview */}
      {selectedTemplateObj && (
        <Card className="border-pink-200 dark:border-pink-700">
          <CardHeader>
            <CardTitle className="text-pink-800 dark:text-pink-300">Template Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                {selectedTemplateObj.name}
              </h4>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                {selectedTemplateObj.description}
              </p>
              {selectedTemplateObj.requiresData && (
                <div className="text-xs text-amber-600 dark:text-amber-400 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  This template requires additional data fields
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Send Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSendTestEmail}
          disabled={sending || !selectedTemplate || !recipientEmail}
          className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
        >
          {sending ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Sending...
            </div>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Send Test Email
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
