"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Plus, Upload, Star, Heart, Eye, EyeOff, Trash2, Edit } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface NailImage {
  id: string
  url: string
  service_name: string
  client_name: string | null
  date_created: string
  featured: boolean
  public: boolean
  likes_count: number
  review_id: string | null
  review_text: string | null
  review_rating: number | null
}

export function AdminMyWork() {
  const [images, setImages] = useState<NailImage[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [isUploadOpen, setIsUploadOpen] = useState(false)
  const [formData, setFormData] = useState({
    url: '',
    service_name: '',
    client_name: '',
    featured: false,
    public: true
  })

  useEffect(() => {
    fetchImages()
  }, [])

  const fetchImages = async () => {
    try {
      const { data, error } = await supabase
        .from('nail_images')
        .select(`
          *,
          customer_reviews!left(
            id,
            review_text,
            rating
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching images:', error)
        toast.error('Failed to load images')
        return
      }

      // Transform the data to include review information
      const transformedImages = data?.map(image => ({
        id: image.id,
        url: image.url,
        service_name: image.service_name,
        client_name: image.client_name,
        date_created: image.created_at,
        featured: image.featured,
        public: image.public,
        likes_count: 0, // We'll need to count this from image_likes table
        review_id: image.customer_reviews?.[0]?.id || null,
        review_text: image.customer_reviews?.[0]?.review_text || null,
        review_rating: image.customer_reviews?.[0]?.rating || null
      })) || []

      setImages(transformedImages)
    } catch (error) {
      console.error('Error fetching images:', error)
      toast.error('Failed to load images')
    } finally {
      setLoading(false)
    }
  }

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.url.trim() || !formData.service_name.trim()) {
      toast.error('Please fill in all required fields')
      return
    }

    setUploading(true)
    try {
      const { error } = await supabase
        .from('nail_images')
        .insert({
          url: formData.url.trim(),
          service_name: formData.service_name.trim(),
          client_name: formData.client_name.trim() || null,
          featured: formData.featured,
          public: formData.public,
          user_id: null // Admin uploaded
        })

      if (error) {
        console.error('Error uploading image:', error)
        toast.error('Failed to upload image')
        return
      }

      toast.success('Image uploaded successfully!')
      setFormData({
        url: '',
        service_name: '',
        client_name: '',
        featured: false,
        public: true
      })
      setIsUploadOpen(false)
      fetchImages()
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image')
    } finally {
      setUploading(false)
    }
  }

  const toggleFeatured = async (imageId: string, currentFeatured: boolean) => {
    try {
      const { error } = await supabase
        .from('nail_images')
        .update({ featured: !currentFeatured })
        .eq('id', imageId)

      if (error) {
        console.error('Error toggling featured:', error)
        toast.error('Failed to update image')
        return
      }

      toast.success(`Image ${!currentFeatured ? 'featured' : 'unfeatured'}`)
      fetchImages()
    } catch (error) {
      console.error('Error toggling featured:', error)
      toast.error('Failed to update image')
    }
  }

  const togglePublic = async (imageId: string, currentPublic: boolean) => {
    try {
      const { error } = await supabase
        .from('nail_images')
        .update({ public: !currentPublic })
        .eq('id', imageId)

      if (error) {
        console.error('Error toggling public:', error)
        toast.error('Failed to update image')
        return
      }

      toast.success(`Image ${!currentPublic ? 'made public' : 'made private'}`)
      fetchImages()
    } catch (error) {
      console.error('Error toggling public:', error)
      toast.error('Failed to update image')
    }
  }

  const deleteImage = async (imageId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) return

    try {
      const { error } = await supabase
        .from('nail_images')
        .delete()
        .eq('id', imageId)

      if (error) {
        console.error('Error deleting image:', error)
        toast.error('Failed to delete image')
        return
      }

      toast.success('Image deleted successfully!')
      fetchImages()
    } catch (error) {
      console.error('Error deleting image:', error)
      toast.error('Failed to delete image')
    }
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 max-w-none w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">My Work</h2>
          <p className="text-gray-600 dark:text-gray-400">Manage your portfolio and featured nail art</p>
        </div>
        <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              <Plus className="h-4 w-4 mr-2" />
              Upload Image
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="text-pink-800 dark:text-pink-300">Upload New Image</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleUpload} className="space-y-4">
              <div>
                <Label htmlFor="url" className="text-pink-800 dark:text-pink-300">
                  Image URL *
                </Label>
                <Input
                  id="url"
                  value={formData.url}
                  onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                  placeholder="https://example.com/image.jpg"
                  className="border-pink-200 dark:border-pink-700"
                  required
                />
              </div>

              <div>
                <Label htmlFor="service_name" className="text-pink-800 dark:text-pink-300">
                  Service Name *
                </Label>
                <Input
                  id="service_name"
                  value={formData.service_name}
                  onChange={(e) => setFormData({ ...formData, service_name: e.target.value })}
                  placeholder="e.g., Gel Manicure with Art"
                  className="border-pink-200 dark:border-pink-700"
                  required
                />
              </div>

              <div>
                <Label htmlFor="client_name" className="text-pink-800 dark:text-pink-300">
                  Client Name (Optional)
                </Label>
                <Input
                  id="client_name"
                  value={formData.client_name}
                  onChange={(e) => setFormData({ ...formData, client_name: e.target.value })}
                  placeholder="Client's name"
                  className="border-pink-200 dark:border-pink-700"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.featured}
                  onCheckedChange={(checked) => setFormData({ ...formData, featured: checked })}
                />
                <Label className="text-pink-800 dark:text-pink-300">Featured</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.public}
                  onCheckedChange={(checked) => setFormData({ ...formData, public: checked })}
                />
                <Label className="text-pink-800 dark:text-pink-300">Public</Label>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsUploadOpen(false)}
                  className="border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={uploading}
                  className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  {uploading ? 'Uploading...' : 'Upload'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Images Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {images.map((image) => (
          <Card key={image.id} className="border-pink-200 dark:border-pink-700 overflow-hidden">
            <div className="relative">
              <img
                src={image.url}
                alt={image.service_name}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-2 left-2 flex space-x-1">
                {image.featured && (
                  <Badge className="bg-amber-500 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                )}
                {image.public ? (
                  <Badge className="bg-green-500 text-white">
                    <Eye className="h-3 w-3 mr-1" />
                    Public
                  </Badge>
                ) : (
                  <Badge className="bg-gray-500 text-white">
                    <EyeOff className="h-3 w-3 mr-1" />
                    Private
                  </Badge>
                )}
              </div>
              <div className="absolute top-2 right-2 flex space-x-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => toggleFeatured(image.id, image.featured)}
                  className="bg-white/90 border-amber-300 text-amber-700 hover:bg-amber-50"
                >
                  <Star className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => togglePublic(image.id, image.public)}
                  className="bg-white/90 border-blue-300 text-blue-700 hover:bg-blue-50"
                >
                  {image.public ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => deleteImage(image.id)}
                  className="bg-white/90 border-red-300 text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">{image.service_name}</h3>
              {image.client_name && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Client: {image.client_name}</p>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">
                {new Date(image.date_created).toLocaleDateString()}
              </p>
              
              {image.review_text && (
                <div className="mt-3 p-2 bg-pink-50 dark:bg-pink-900/20 rounded">
                  <div className="flex items-center mb-1">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-3 w-3 ${
                            i < (image.review_rating || 0)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-xs text-gray-700 dark:text-gray-300">{image.review_text}</p>
                </div>
              )}
              
              <div className="flex items-center justify-between mt-3">
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <Heart className="h-4 w-4 mr-1" />
                  {image.likes_count} likes
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {images.length === 0 && (
        <Card className="border-pink-200 dark:border-pink-700">
          <CardContent className="p-6 text-center">
            <Upload className="h-12 w-12 text-pink-300 dark:text-pink-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 dark:text-pink-300 mb-2">No Images Yet</h3>
            <p className="text-pink-600 dark:text-pink-400">Upload your first nail art image to get started!</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
