"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Star, ChevronRight } from 'lucide-react'
import { supabase } from '@/lib/supabase'

interface CustomerReview {
  id: string
  customer_name: string
  rating: number
  review_text: string
  service_name: string | null
  review_date: string
  featured: boolean
}

interface CustomerReviewsProps {
  showTitle?: boolean
  limit?: number
  showViewAll?: boolean
  featuredOnly?: boolean
}

export function CustomerReviews({
  showTitle = true,
  limit = 3,
  showViewAll = true,
  featuredOnly = false
}: CustomerReviewsProps) {
  const [reviews, setReviews] = useState<CustomerReview[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchReviews()
  }, [limit, featuredOnly])

  const fetchReviews = async () => {
    try {
      setError(null)
      let query = supabase
        .from('customer_reviews')
        .select('*')
        .eq('approved', true)
        .order('review_date', { ascending: false })

      if (featuredOnly) {
        query = query.eq('featured', true)
      }

      if (limit) {
        query = query.limit(limit)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching reviews:', error)
        setError('Failed to load customer reviews. Please try again later.')
        return
      }

      setReviews(data || [])
    } catch (error) {
      console.error('Error fetching reviews:', error)
      setError('Failed to load customer reviews. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-amber-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {showTitle && <div className="h-6 bg-gray-200 rounded animate-pulse" />}
        <div className="space-y-3">
          {[...Array(limit)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        {showTitle && (
          <h2 className="text-xl font-bold text-gray-900">What Clients Say</h2>
        )}
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4 text-center">
            <p className="text-red-700 text-sm mb-2">{error}</p>
            <Button
              size="sm"
              variant="outline"
              onClick={fetchReviews}
              className="border-red-200 text-red-700 hover:bg-red-100"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (reviews.length === 0) {
    // Fallback to default reviews if none are configured
    const defaultReviews = [
      {
        customer_name: "Sarah Johnson",
        rating: 5,
        review_text: "Absolutely amazing experience! The nail art was exactly what I wanted and the service was top-notch.",
        service_name: "Intricate Nail Art",
        review_date: "2024-01-10"
      },
      {
        customer_name: "Emily Davis",
        rating: 5,
        review_text: "Best manicure I've ever had! The attention to detail is incredible and the salon is so relaxing.",
        service_name: "Luxury Spa Manicure",
        review_date: "2024-01-08"
      },
      {
        customer_name: "Jessica Wilson",
        rating: 5,
        review_text: "The diamond manicure was worth every penny. My nails have never looked better!",
        service_name: "Diamond Manicure",
        review_date: "2024-01-05"
      }
    ]

    return (
      <div className="space-y-4">
        {showTitle && (
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">What Clients Say</h2>
            {showViewAll && (
              <Button variant="ghost" size="sm" className="text-pink-600">
                View All
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            )}
          </div>
        )}

        <div className="space-y-3">
          {defaultReviews.slice(0, limit).map((review, index) => (
            <Card key={index} className="border-pink-200 bg-white/80 backdrop-blur-sm">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-pink-800">{review.customer_name}</h4>
                    <div className="flex items-center mt-1">
                      {renderStars(review.rating)}
                    </div>
                  </div>
                  {review.service_name && (
                    <span className="text-xs text-pink-600 bg-pink-100 px-2 py-1 rounded">
                      {review.service_name}
                    </span>
                  )}
                </div>
                <p className="text-gray-700 text-sm leading-relaxed">{review.review_text}</p>
                <p className="text-xs text-gray-500 mt-2">
                  {new Date(review.review_date).toLocaleDateString()}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {showTitle && (
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">What Clients Say</h2>
          {showViewAll && reviews.length > 0 && (
            <Button variant="ghost" size="sm" className="text-pink-600">
              View All
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          )}
        </div>
      )}

      <div className="space-y-3">
        {reviews.map((review) => (
          <Card key={review.id} className="border-pink-200 bg-white/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="font-semibold text-pink-800">{review.customer_name}</h4>
                  <div className="flex items-center mt-1">
                    {renderStars(review.rating)}
                  </div>
                </div>
                {review.service_name && (
                  <span className="text-xs text-pink-600 bg-pink-100 px-2 py-1 rounded">
                    {review.service_name}
                  </span>
                )}
              </div>
              <p className="text-gray-700 text-sm leading-relaxed">{review.review_text}</p>
              <p className="text-xs text-gray-500 mt-2">
                {new Date(review.review_date).toLocaleDateString()}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
