"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, BarChart3, Users, Calendar, Sparkles, Star } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface HomepageStat {
  id: string
  title: string
  value: string
  description: string
  icon: string
  display_order: number
  active: boolean
}

const iconOptions = [
  { value: 'users', label: 'Users', icon: Users },
  { value: 'calendar', label: 'Calendar', icon: Calendar },
  { value: 'sparkles', label: 'Sparkles', icon: Sparkles },
  { value: 'star', label: 'Star', icon: Star },
  { value: 'bar-chart', label: 'Bar Chart', icon: BarChart3 },
]

export function AdminHomepageStats() {
  const [stats, setStats] = useState<HomepageStat[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [editingStat, setEditingStat] = useState<HomepageStat | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    value: '',
    description: '',
    icon: 'users',
    display_order: 1,
    active: true
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('homepage_stats')
        .select('*')
        .order('display_order', { ascending: true })

      if (error) {
        console.error('Error fetching stats:', error)
        toast.error('Failed to load homepage stats')
        return
      }

      setStats(data || [])
    } catch (error) {
      console.error('Error fetching stats:', error)
      toast.error('Failed to load homepage stats')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      title: '',
      value: '',
      description: '',
      icon: 'users',
      display_order: stats.length + 1,
      active: true
    })
    setEditingStat(null)
  }

  const handleCreate = () => {
    resetForm()
    setIsCreateOpen(true)
  }

  const handleEdit = (stat: HomepageStat) => {
    setFormData({
      title: stat.title,
      value: stat.value,
      description: stat.description,
      icon: stat.icon,
      display_order: stat.display_order,
      active: stat.active
    })
    setEditingStat(stat)
    setIsCreateOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title.trim() || !formData.value.trim() || !formData.description.trim()) {
      toast.error('Please fill in all fields')
      return
    }

    try {
      if (editingStat) {
        // Update existing stat
        const { error } = await supabase
          .from('homepage_stats')
          .update({
            title: formData.title.trim(),
            value: formData.value.trim(),
            description: formData.description.trim(),
            icon: formData.icon,
            display_order: formData.display_order,
            active: formData.active
          })
          .eq('id', editingStat.id)

        if (error) {
          console.error('Error updating stat:', error)
          toast.error('Failed to update stat')
          return
        }

        toast.success('Stat updated successfully!')
      } else {
        // Create new stat
        const { error } = await supabase
          .from('homepage_stats')
          .insert({
            title: formData.title.trim(),
            value: formData.value.trim(),
            description: formData.description.trim(),
            icon: formData.icon,
            display_order: formData.display_order,
            active: formData.active
          })

        if (error) {
          console.error('Error creating stat:', error)
          toast.error('Failed to create stat')
          return
        }

        toast.success('Stat created successfully!')
      }

      setIsCreateOpen(false)
      resetForm()
      fetchStats()
    } catch (error) {
      console.error('Error saving stat:', error)
      toast.error('Failed to save stat')
    }
  }

  const handleDelete = async (statId: string) => {
    if (!confirm('Are you sure you want to delete this stat?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('homepage_stats')
        .delete()
        .eq('id', statId)

      if (error) {
        console.error('Error deleting stat:', error)
        toast.error('Failed to delete stat')
        return
      }

      toast.success('Stat deleted successfully!')
      fetchStats()
    } catch (error) {
      console.error('Error deleting stat:', error)
      toast.error('Failed to delete stat')
    }
  }

  const toggleActive = async (statId: string, currentActive: boolean) => {
    try {
      const { error } = await supabase
        .from('homepage_stats')
        .update({ active: !currentActive })
        .eq('id', statId)

      if (error) {
        console.error('Error toggling stat:', error)
        toast.error('Failed to update stat')
        return
      }

      toast.success(`Stat ${!currentActive ? 'activated' : 'deactivated'}`)
      fetchStats()
    } catch (error) {
      console.error('Error toggling stat:', error)
      toast.error('Failed to update stat')
    }
  }

  const getIcon = (iconName: string) => {
    const iconOption = iconOptions.find(opt => opt.value === iconName)
    return iconOption ? iconOption.icon : Users
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Homepage Stats</h2>
          <p className="text-gray-600">Manage the quick stats displayed on the homepage</p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleCreate} className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              <Plus className="h-4 w-4 mr-2" />
              Add Stat
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white max-w-md">
            <DialogHeader>
              <DialogTitle className="text-pink-800">
                {editingStat ? 'Edit Stat' : 'Create New Stat'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-pink-800">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="e.g., Happy Clients"
                  required
                />
              </div>
              <div>
                <Label htmlFor="value" className="text-pink-800">Value</Label>
                <Input
                  id="value"
                  value={formData.value}
                  onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="e.g., 500+"
                  required
                />
              </div>
              <div>
                <Label htmlFor="description" className="text-pink-800">Description</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="e.g., Satisfied customers who love our work"
                  required
                />
              </div>
              <div>
                <Label htmlFor="icon" className="text-pink-800">Icon</Label>
                <Select value={formData.icon} onValueChange={(value) => setFormData({ ...formData, icon: value })}>
                  <SelectTrigger className="border-pink-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent side="bottom" align="start" sideOffset={5}>
                    {iconOptions.map((option) => {
                      const IconComponent = option.icon
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center">
                            <IconComponent className="h-4 w-4 mr-2" />
                            {option.label}
                          </div>
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="display_order" className="text-pink-800">Display Order</Label>
                  <Input
                    id="display_order"
                    type="number"
                    min="1"
                    value={formData.display_order}
                    onChange={(e) => setFormData({ ...formData, display_order: parseInt(e.target.value) || 1 })}
                    className="border-pink-200 focus:border-pink-500"
                  />
                </div>
                <div className="flex items-center space-x-2 mt-6">
                  <Switch
                    id="active"
                    checked={formData.active}
                    onCheckedChange={(checked) => setFormData({ ...formData, active: checked })}
                  />
                  <Label htmlFor="active" className="text-pink-800">Active</Label>
                </div>
              </div>
              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateOpen(false)}
                  className="flex-1 border-pink-300 text-pink-700"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  {editingStat ? 'Update' : 'Create'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {stats.map((stat) => {
          const IconComponent = getIcon(stat.icon)
          return (
            <Card key={stat.id} className="border-pink-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <IconComponent className="h-5 w-5 text-pink-600" />
                  <CardTitle className="text-sm font-medium text-pink-800">{stat.title}</CardTitle>
                </div>
                <div className="flex items-center space-x-2">
                  {stat.active ? (
                    <Badge className="bg-green-100 text-green-800">Active</Badge>
                  ) : (
                    <Badge variant="outline" className="border-gray-300 text-gray-600">Inactive</Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-pink-800 mb-1">{stat.value}</div>
                <p className="text-xs text-pink-600 mb-4">{stat.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">Order: {stat.display_order}</span>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => toggleActive(stat.id, stat.active)}
                      variant="outline"
                      size="sm"
                      className="text-xs"
                    >
                      {stat.active ? 'Deactivate' : 'Activate'}
                    </Button>
                    <Button
                      onClick={() => handleEdit(stat)}
                      variant="outline"
                      size="sm"
                      className="text-xs"
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      onClick={() => handleDelete(stat.id)}
                      variant="outline"
                      size="sm"
                      className="text-xs text-red-600 border-red-300 hover:bg-red-50"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {stats.length === 0 && (
        <Card className="border-pink-200">
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-12 w-12 text-pink-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 mb-2">No Stats Created</h3>
            <p className="text-pink-600">Create your first homepage stat to get started!</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
