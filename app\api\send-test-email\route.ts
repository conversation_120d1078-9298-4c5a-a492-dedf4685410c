import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { 
  sendEmail, 
  createEmailVerificationTemplate,
  createMagicLinkTemplate,
  createPasswordResetTemplate,
  createAppointmentConfirmationTemplate,
  createReferralInvitationTemplate,
  sendAnnouncementEmail
} from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const { templateId, recipientEmail, templateData } = await request.json()

    // Verify the request is from an admin
    const supabase = createServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: userProfile, error: profileError } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError || !userProfile?.is_admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Validate input
    if (!templateId || !recipientEmail) {
      return NextResponse.json({ error: 'Template ID and recipient email are required' }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(recipientEmail)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 })
    }

    let template
    let result

    // Generate the appropriate template based on templateId
    switch (templateId) {
      case 'verification':
        template = createEmailVerificationTemplate(
          recipientEmail,
          'https://example.com/verify?token=test-token'
        )
        result = await sendEmail(template)
        break

      case 'magic-link':
        template = createMagicLinkTemplate(
          recipientEmail,
          'https://example.com/auth/callback?token=test-magic-link'
        )
        result = await sendEmail(template)
        break

      case 'password-reset':
        template = createPasswordResetTemplate(
          recipientEmail,
          'https://example.com/reset-password?token=test-reset-token'
        )
        result = await sendEmail(template)
        break

      case 'appointment-confirmation':
        if (!templateData?.appointmentDate || !templateData?.appointmentTime || !templateData?.services || !templateData?.totalPrice) {
          return NextResponse.json({ error: 'Missing required appointment data' }, { status: 400 })
        }
        template = createAppointmentConfirmationTemplate(
          recipientEmail,
          {
            date: templateData.appointmentDate,
            time: templateData.appointmentTime,
            services: templateData.services.split(',').map((s: string) => s.trim()),
            totalPrice: parseFloat(templateData.totalPrice),
            duration: 90 // Default duration for test
          }
        )
        result = await sendEmail(template)
        break

      case 'announcement':
        if (!templateData?.title || !templateData?.content) {
          return NextResponse.json({ error: 'Missing required announcement data' }, { status: 400 })
        }
        result = await sendAnnouncementEmail(
          [recipientEmail],
          templateData.title,
          templateData.content
        )
        break

      case 'referral-invitation':
        if (!templateData?.senderName || !templateData?.referralCode) {
          return NextResponse.json({ error: 'Missing required referral data' }, { status: 400 })
        }
        template = createReferralInvitationTemplate(
          recipientEmail,
          templateData.senderName,
          templateData.referralCode
        )
        result = await sendEmail(template)
        break

      default:
        return NextResponse.json({ error: 'Invalid template ID' }, { status: 400 })
    }

    if (!result.success) {
      console.error('Failed to send test email:', result.error)
      return NextResponse.json({ 
        error: 'Failed to send test email', 
        details: result.error 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: `Test email sent successfully to ${recipientEmail}`,
      data: result.data 
    })

  } catch (error) {
    console.error('Send test email error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
