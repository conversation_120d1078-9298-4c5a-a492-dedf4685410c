import { loadStripe } from '@stripe/stripe-js'
import Stripe from 'stripe'

// Client-side Stripe instance
export const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

// Server-side Stripe instance
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
})

// Types for Stripe payment
export interface PaymentIntentData {
  amount: number
  currency: string
  metadata?: Record<string, string>
}

export interface CreatePaymentIntentResponse {
  clientSecret: string
  paymentIntentId: string
}

// Helper function to create payment intent
export async function createPaymentIntent(data: PaymentIntentData): Promise<CreatePaymentIntentResponse> {
  const paymentIntent = await stripe.paymentIntents.create({
    amount: Math.round(data.amount * 100), // Convert to cents
    currency: data.currency || 'usd',
    metadata: data.metadata || {},
    automatic_payment_methods: {
      enabled: true,
    },
  })

  return {
    clientSecret: paymentIntent.client_secret!,
    paymentIntentId: paymentIntent.id,
  }
}

// Helper function to confirm payment
export async function confirmPayment(paymentIntentId: string) {
  return await stripe.paymentIntents.retrieve(paymentIntentId)
}
