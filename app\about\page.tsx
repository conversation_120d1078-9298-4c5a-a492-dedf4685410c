"use client"

import { useState, useEffect } from 'react'
import { AppHeader } from "@/components/ui/app-header"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { User, Star, Award, ExternalLink, Instagram, Facebook, Globe } from "lucide-react"
import { supabase } from '@/lib/supabase'
import Link from 'next/link'

interface OwnerProfile {
  id: string
  name: string
  title: string
  bio: string | null
  photo_url: string | null
  years_experience: number | null
  specialties: string[] | null
  certifications: string[] | null
  social_links: {
    instagram?: string
    facebook?: string
    website?: string
  } | null
}

export default function AboutPage() {
  const [profile, setProfile] = useState<OwnerProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchOwnerProfile()
  }, [])

  const fetchOwnerProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('owner_profile')
        .select('*')
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching owner profile:', error)
        return
      }

      setProfile(data)
    } catch (error) {
      console.error('Error fetching owner profile:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <AppHeader title="About" showBack />
        <div className="px-4 py-6 space-y-6">
          <div className="animate-pulse">
            <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <AppHeader title="About" showBack />
        <div className="px-4 py-6">
          <Card className="border-pink-200 dark:border-pink-700">
            <CardContent className="p-6 text-center">
              <User className="h-12 w-12 text-pink-300 dark:text-pink-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-pink-800 dark:text-pink-300 mb-2">About Page Coming Soon</h3>
              <p className="text-pink-600 dark:text-pink-400">
                The owner profile information will be available soon!
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20">
      <AppHeader title="About" showBack />

      <div className="px-4 py-6 space-y-6">

        {/* Owner Profile Header */}
        <Card className="border-pink-200 dark:border-pink-700 dark:bg-gray-800">
          <CardContent className="p-6">
            <div className="flex flex-col items-center text-center">
              <div className="w-24 h-24 mb-4">
                {profile.photo_url ? (
                  <img
                    src={profile.photo_url}
                    alt={profile.name}
                    className="w-24 h-24 rounded-full object-cover border-4 border-pink-200 dark:border-pink-700"
                  />
                ) : (
                  <div className="w-24 h-24 bg-gradient-to-r from-pink-500 to-amber-500 rounded-full flex items-center justify-center">
                    <User className="h-12 w-12 text-white" />
                  </div>
                )}
              </div>

              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                {profile.name}
              </h1>

              <p className="text-lg text-pink-600 dark:text-pink-400 mb-3">
                {profile.title}
              </p>

              {profile.years_experience && (
                <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200 mb-4">
                  <Star className="h-3 w-3 mr-1" />
                  {profile.years_experience}+ Years Experience
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Bio Section */}
        {profile.bio && (
          <Card className="border-pink-200 dark:border-pink-700 dark:bg-gray-800">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">About Me</h2>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                {profile.bio}
              </p>
            </CardContent>
          </Card>
        )}

        {/* Specialties */}
        {profile.specialties && profile.specialties.length > 0 && (
          <Card className="border-pink-200 dark:border-pink-700 dark:bg-gray-800">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Specialties</h2>
              <div className="flex flex-wrap gap-2">
                {profile.specialties.map((specialty, index) => (
                  <Badge key={index} variant="outline" className="border-pink-300 text-pink-700 dark:border-pink-600 dark:text-pink-400">
                    {specialty}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Certifications */}
        {profile.certifications && profile.certifications.length > 0 && (
          <Card className="border-pink-200 dark:border-pink-700 dark:bg-gray-800">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Certifications</h2>
              <div className="space-y-2">
                {profile.certifications.map((certification, index) => (
                  <div key={index} className="flex items-center">
                    <Award className="h-4 w-4 text-amber-500 mr-2" />
                    <span className="text-gray-700 dark:text-gray-300">{certification}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Social Links */}
        {profile.social_links && Object.keys(profile.social_links).length > 0 && (
          <Card className="border-pink-200 dark:border-pink-700 dark:bg-gray-800">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3">Connect With Me</h2>
              <div className="space-y-3">
                {profile.social_links.instagram && (
                  <Button
                    asChild
                    variant="outline"
                    className="w-full justify-start border-pink-300 text-pink-700 hover:bg-pink-50 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900/20"
                  >
                    <a href={profile.social_links.instagram} target="_blank" rel="noopener noreferrer">
                      <Instagram className="h-4 w-4 mr-2" />
                      Follow on Instagram
                      <ExternalLink className="h-3 w-3 ml-auto" />
                    </a>
                  </Button>
                )}

                {profile.social_links.facebook && (
                  <Button
                    asChild
                    variant="outline"
                    className="w-full justify-start border-pink-300 text-pink-700 hover:bg-pink-50 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900/20"
                  >
                    <a href={profile.social_links.facebook} target="_blank" rel="noopener noreferrer">
                      <Facebook className="h-4 w-4 mr-2" />
                      Visit Facebook Page
                      <ExternalLink className="h-3 w-3 ml-auto" />
                    </a>
                  </Button>
                )}

                {profile.social_links.website && (
                  <Button
                    asChild
                    variant="outline"
                    className="w-full justify-start border-pink-300 text-pink-700 hover:bg-pink-50 dark:border-pink-600 dark:text-pink-400 dark:hover:bg-pink-900/20"
                  >
                    <a href={profile.social_links.website} target="_blank" rel="noopener noreferrer">
                      <Globe className="h-4 w-4 mr-2" />
                      Visit Website
                      <ExternalLink className="h-3 w-3 ml-auto" />
                    </a>
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Call to Action */}
        <Card className="border-pink-200 dark:border-pink-700 bg-gradient-to-r from-pink-50 to-amber-50 dark:from-pink-900/20 dark:to-amber-900/20">
          <CardContent className="p-6 text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Ready to Book?</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Experience professional nail care and artistry
            </p>
            <Button asChild className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              <Link href="/booking">
                Book Appointment
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
