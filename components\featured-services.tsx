"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChevronRight, Star, Sparkles } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import Link from 'next/link'

interface Service {
  id: string
  name: string
  price: number
  duration: number
  description: string | null
  image: string | null
  featured: boolean
  active: boolean
}

interface ServiceCombination {
  id: string
  name: string
  service_ids: string[]
  discount_type: 'percentage' | 'fixed'
  discount_value: number
  description: string | null
  featured: boolean
  active: boolean
  created_at: string
}

interface FeaturedServicesProps {
  limit?: number
  showPopular?: boolean // Show popular items instead of featured
}

export function FeaturedServices({ limit = 4, showPopular = false }: FeaturedServicesProps) {
  const [services, setServices] = useState<Service[]>([])
  const [combinations, setCombinations] = useState<ServiceCombination[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchFeaturedItems()
  }, [])

  const fetchFeaturedItems = async () => {
    try {
      setError(null)

      const filterField = showPopular ? 'popular' : 'featured'

      // Fetch services (popular or featured)
      const { data: servicesData, error: servicesError } = await supabase
        .from('services')
        .select('*')
        .eq(filterField, true)
        .eq('active', true)
        .order('created_at', { ascending: false })

      if (servicesError) {
        console.error(`Error fetching ${filterField} services:`, servicesError)
      }

      // Fetch service combinations (popular or featured)
      const { data: combinationsData, error: combinationsError } = await supabase
        .from('service_combinations')
        .select('*')
        .eq(filterField, true)
        .eq('active', true)
        .order('created_at', { ascending: false })

      if (combinationsError) {
        console.error(`Error fetching ${filterField} combinations:`, combinationsError)
      }

      setServices(servicesData || [])
      setCombinations(combinationsData || [])
    } catch (error) {
      console.error(`Error fetching ${showPopular ? 'popular' : 'featured'} items:`, error)
      setError(`Failed to load ${showPopular ? 'popular' : 'featured'} services`)
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return `$${price.toFixed(0)}`
  }

  const formatDuration = (duration: number) => {
    const hours = Math.floor(duration / 60)
    const minutes = duration % 60
    if (hours > 0) {
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
    }
    return `${minutes}m`
  }

  const calculateCombinationPrice = (combination: ServiceCombination) => {
    // This would need to calculate based on the actual services
    // For now, we'll use a placeholder calculation
    const basePrice = 100 // This should be calculated from actual services
    if (combination.discount_type === 'percentage') {
      return basePrice * (1 - combination.discount_value / 100)
    } else {
      return basePrice - combination.discount_value
    }
  }

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="border-0 shadow-sm">
            <CardContent className="p-0">
              <div className="flex items-center">
                <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-l-lg animate-pulse" />
                <div className="flex-1 p-4">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse" />
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <Sparkles className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
        <p className="text-gray-500 dark:text-gray-400">Unable to load featured services</p>
      </div>
    )
  }

  // Combine services and combinations, then limit
  const allFeaturedItems = [
    ...services.map(service => ({ ...service, type: 'service' as const })),
    ...combinations.map(combination => ({ ...combination, type: 'combination' as const }))
  ].slice(0, limit)

  if (allFeaturedItems.length === 0) {
    return (
      <div className="text-center py-8">
        <Sparkles className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No featured services available</p>
        <p className="text-sm text-gray-400 dark:text-gray-500">Check back soon for our featured offerings!</p>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {allFeaturedItems.map((item, index) => (
        <Card key={`${item.type}-${item.id}`} className="border-0 shadow-sm dark:bg-gray-800">
          <CardContent className="p-0">
            <div className="flex items-center">
              <img
                src={('image' in item ? item.image : null) || "/placeholder.svg?height=80&width=80&text=Service"}
                alt={item.name}
                className="w-20 h-20 object-cover rounded-l-lg"
              />
              <div className="flex-1 p-4">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">{item.name}</h3>
                  <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                    <Star className="h-3 w-3 mr-1" />
                    {showPopular ? 'Popular' : 'Featured'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold text-pink-600 dark:text-pink-400">
                    {item.type === 'service' 
                      ? formatPrice(item.price) 
                      : formatPrice(calculateCombinationPrice(item as ServiceCombination))
                    }
                  </span>
                  {item.type === 'service' && (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {formatDuration(item.duration)}
                    </span>
                  )}
                  {item.type === 'combination' && (
                    <Badge variant="outline" className="text-xs">
                      Combo Deal
                    </Badge>
                  )}
                </div>
                {item.description && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-1">
                    {item.description}
                  </p>
                )}
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400 dark:text-gray-500 mr-3" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
