import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabase<PERSON>non<PERSON>ey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Server-side client for admin operations
export const createServerClient = () => {
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Database types
export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  avatar_url?: string
  phone?: string
  is_admin: boolean
  is_member: boolean
  subscribe_announcements: boolean
  email_verified: boolean
  created_at: string
  updated_at: string
}

export interface Service {
  id: string
  name: string
  description: string
  price: number
  duration: number
  category: string
  image_url?: string
  active: boolean
  created_at: string
  updated_at: string
}

export interface Appointment {
  id: string
  user_id: string
  service_ids: string[]
  appointment_date: string
  appointment_time: string
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled' | 'no_show'
  total_price: number
  total_duration: number
  notes?: string
  created_at: string
  updated_at: string
  user?: User
  services?: Service[]
}

export interface NailImage {
  id: string
  user_id: string
  appointment_id: string
  image_url: string
  is_private: boolean
  is_featured: boolean
  likes_count: number
  created_at: string
  updated_at: string
  user?: User
  appointment?: Appointment
}

export interface ImageLike {
  id: string
  user_id: string
  image_id: string
  created_at: string
}

export interface Announcement {
  id: string
  title: string
  content: string
  sent_at?: string
  created_by: string
  created_at: string
  updated_at: string
}

export interface BusinessHours {
  id: string
  day_of_week: number // 0 = Sunday, 1 = Monday, etc.
  open_time?: string
  close_time?: string
  is_closed: boolean
  created_at: string
  updated_at: string
}

export interface BlockedDate {
  id: string
  date: string
  reason: string
  type: 'full_day' | 'partial'
  blocked_times?: string[]
  created_at: string
  updated_at: string
}
