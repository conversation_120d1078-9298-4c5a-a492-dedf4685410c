"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Plus, Send, Calendar, Users, Mail, Trash2 } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/components/auth/auth-provider'
import { toast } from 'sonner'

interface Announcement {
  id: string
  title: string
  content: string
  sent_at: string | null
  created_at: string
  created_by: string
  user: {
    first_name: string
    last_name: string
  }
}

export function AdminAnnouncements() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    content: ''
  })
  const [subscriberCount, setSubscriberCount] = useState(0)
  const { user } = useAuth()

  useEffect(() => {
    fetchAnnouncements()
    fetchSubscriberCount()
  }, [])

  const fetchAnnouncements = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select(`
          id,
          title,
          content,
          sent_at,
          created_at,
          created_by,
          user:users!announcements_created_by_fkey (
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching announcements:', error)
        toast.error('Failed to load announcements')
        return
      }

      // Transform the data to handle the user relationship correctly
      const transformedData = (data || []).map(announcement => ({
        ...announcement,
        user: Array.isArray(announcement.user) ? announcement.user[0] : announcement.user
      }))
      setAnnouncements(transformedData)
    } catch (error) {
      console.error('Error fetching announcements:', error)
      toast.error('Failed to load announcements')
    } finally {
      setLoading(false)
    }
  }

  const fetchSubscriberCount = async () => {
    try {
      const { count, error } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('subscribe_announcements', true)

      if (error) {
        console.error('Error fetching subscriber count:', error)
        return
      }

      setSubscriberCount(count || 0)
    } catch (error) {
      console.error('Error fetching subscriber count:', error)
    }
  }

  const handleCreateAnnouncement = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim() || !formData.content.trim()) {
      toast.error('Please fill in all fields')
      return
    }

    if (!user) {
      toast.error('User not authenticated')
      return
    }

    try {
      const { error } = await supabase
        .from('announcements')
        .insert({
          title: formData.title.trim(),
          content: formData.content.trim(),
          created_by: user.id
        })

      if (error) {
        console.error('Error creating announcement:', error)
        toast.error('Failed to create announcement')
        return
      }

      toast.success('Announcement created successfully!')
      setFormData({ title: '', content: '' })
      setIsCreateOpen(false)
      fetchAnnouncements()
    } catch (error) {
      console.error('Error creating announcement:', error)
      toast.error('Failed to create announcement')
    }
  }

  const handleSendAnnouncement = async (announcementId: string) => {
    setIsSending(true)
    try {
      // Get announcement details
      const { data: announcement, error: fetchError } = await supabase
        .from('announcements')
        .select('title, content')
        .eq('id', announcementId)
        .single()

      if (fetchError || !announcement) {
        toast.error('Failed to fetch announcement details')
        return
      }

      // Get subscribers
      const { data: subscribers, error: subscribersError } = await supabase
        .from('users')
        .select('email')
        .eq('subscribe_announcements', true)

      if (subscribersError) {
        console.error('Error fetching subscribers:', subscribersError)
        toast.error('Failed to fetch subscribers')
        return
      }

      if (!subscribers || subscribers.length === 0) {
        toast.error('No subscribers found')
        return
      }

      // Send emails via API route
      const response = await fetch('/api/send-announcement', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          announcementId,
          title: announcement.title,
          content: announcement.content,
          subscribers: subscribers.map(s => s.email)
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send announcement')
      }

      // Mark announcement as sent
      const { error: updateError } = await supabase
        .from('announcements')
        .update({ sent_at: new Date().toISOString() })
        .eq('id', announcementId)

      if (updateError) {
        console.error('Error updating announcement:', updateError)
        toast.error('Failed to update announcement status')
        return
      }

      toast.success(`Announcement sent to ${subscribers.length} subscribers!`)
      fetchAnnouncements()
    } catch (error) {
      console.error('Error sending announcement:', error)
      toast.error('Failed to send announcement')
    } finally {
      setIsSending(false)
    }
  }

  const handleDeleteAnnouncement = async (announcementId: string) => {
    if (!confirm('Are you sure you want to delete this announcement?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('announcements')
        .delete()
        .eq('id', announcementId)

      if (error) {
        console.error('Error deleting announcement:', error)
        toast.error('Failed to delete announcement')
        return
      }

      toast.success('Announcement deleted successfully!')
      fetchAnnouncements()
    } catch (error) {
      console.error('Error deleting announcement:', error)
      toast.error('Failed to delete announcement')
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="h-32 bg-gray-200 rounded animate-pulse" />
        <div className="h-32 bg-gray-200 rounded animate-pulse" />
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 max-w-none w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Announcements</h2>
          <p className="text-gray-600 dark:text-gray-400">Manage and send announcements to subscribers</p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              <Plus className="h-4 w-4 mr-2" />
              New Announcement
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white max-w-md">
            <DialogHeader>
              <DialogTitle className="text-pink-800">Create Announcement</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateAnnouncement} className="space-y-4">
              <div>
                <Label htmlFor="title" className="text-pink-800">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="Announcement title"
                  required
                />
              </div>
              <div>
                <Label htmlFor="content" className="text-pink-800">Content</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  className="border-pink-200 focus:border-pink-500 min-h-[120px]"
                  placeholder="Announcement content..."
                  required
                />
              </div>
              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateOpen(false)}
                  className="flex-1 border-pink-300 text-pink-700"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  Create
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Subscriber Stats */}
      <Card className="border-pink-200 bg-gradient-to-r from-pink-50 to-amber-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Users className="h-8 w-8 text-pink-600" />
              <div>
                <h3 className="font-semibold text-pink-800">Subscribers</h3>
                <p className="text-sm text-pink-600">Active email subscribers</p>
              </div>
            </div>
            <div className="text-3xl font-bold text-pink-700">{subscriberCount}</div>
          </div>
        </CardContent>
      </Card>

      {/* Announcements List */}
      <div className="space-y-4">
        {announcements.length === 0 ? (
          <Card className="border-pink-200">
            <CardContent className="p-6 text-center">
              <Mail className="h-12 w-12 text-pink-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-pink-800 mb-2">No Announcements</h3>
              <p className="text-pink-600">Create your first announcement to get started!</p>
            </CardContent>
          </Card>
        ) : (
          announcements.map((announcement) => (
            <Card key={announcement.id} className="border-pink-200">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-pink-800">{announcement.title}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-2">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(announcement.created_at).toLocaleDateString()}
                      </div>
                      <div>
                        By {announcement.user.first_name} {announcement.user.last_name}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {announcement.sent_at ? (
                      <Badge className="bg-green-100 text-green-800">
                        Sent {new Date(announcement.sent_at).toLocaleDateString()}
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="border-amber-300 text-amber-700">
                        Draft
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-4">{announcement.content}</p>
                <Separator className="my-4" />
                <div className="flex items-center justify-between">
                  <div className="flex space-x-2">
                    {!announcement.sent_at && (
                      <Button
                        onClick={() => handleSendAnnouncement(announcement.id)}
                        disabled={isSending || subscriberCount === 0}
                        size="sm"
                        className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                      >
                        {isSending ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Sending...
                          </div>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            Send to {subscriberCount} subscribers
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                  <Button
                    onClick={() => handleDeleteAnnouncement(announcement.id)}
                    variant="outline"
                    size="sm"
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
