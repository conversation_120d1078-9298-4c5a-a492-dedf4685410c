import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase'
import { sendEmail, createReferralInvitationTemplate } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const { emails, referralCode, senderName } = await request.json()

    // Verify the request is from an authenticated user
    const supabase = createServerClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Validate input
    if (!Array.isArray(emails) || emails.length === 0) {
      return NextResponse.json({ error: 'No email addresses provided' }, { status: 400 })
    }

    if (!referralCode || !senderName) {
      return NextResponse.json({ error: 'Missing referral code or sender name' }, { status: 400 })
    }

    // Validate email addresses
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const validEmails = emails.filter((email: string) => emailRegex.test(email))
    
    if (validEmails.length === 0) {
      return NextResponse.json({ error: 'No valid email addresses provided' }, { status: 400 })
    }

    if (validEmails.length > 5) {
      return NextResponse.json({ error: 'Maximum 5 email addresses allowed' }, { status: 400 })
    }

    // Send invitation emails
    const results = []
    const errors = []

    for (const email of validEmails) {
      try {
        const template = createReferralInvitationTemplate(email, senderName, referralCode)
        const result = await sendEmail(template)
        
        if (result.success) {
          results.push({ email, success: true })
        } else {
          errors.push({ email, error: result.error })
        }
      } catch (error) {
        console.error(`Error sending invitation to ${email}:`, error)
        errors.push({ email, error: 'Failed to send invitation' })
      }
    }

    // Log the referral invitations in the database (optional)
    try {
      const { error: logError } = await supabase
        .from('referral_invitations')
        .insert(
          validEmails.map(email => ({
            sender_id: user.id,
            recipient_email: email,
            referral_code: referralCode,
            sent_at: new Date().toISOString()
          }))
        )

      if (logError) {
        console.error('Error logging referral invitations:', logError)
        // Don't fail the request if logging fails
      }
    } catch (logError) {
      console.error('Error logging referral invitations:', logError)
      // Don't fail the request if logging fails
    }

    const successCount = results.length
    const errorCount = errors.length

    if (successCount === 0) {
      return NextResponse.json({ 
        error: 'Failed to send any invitations',
        details: errors
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: `Successfully sent ${successCount} invitation${successCount > 1 ? 's' : ''}`,
      results: {
        successful: results,
        failed: errors
      }
    })

  } catch (error) {
    console.error('Error in send-referral-invitations:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
