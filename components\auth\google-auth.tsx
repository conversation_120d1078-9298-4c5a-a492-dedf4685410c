"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Mail, Lock, User, Phone, Eye, EyeOff, Send } from "lucide-react"
import { useAuth } from "./auth-provider"
import { toast } from "sonner"

// Google G Logo Component
const GoogleIcon = () => (
  <svg className="h-5 w-5 mr-3" viewBox="0 0 24 24">
    <path
      fill="#4285F4"
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
    />
    <path
      fill="#34A853"
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
    />
    <path
      fill="#FBBC05"
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
    />
    <path
      fill="#EA4335"
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
    />
  </svg>
)

interface GoogleAuthProps {
  isOpen: boolean
  onClose: () => void
  mode: "signin" | "signup"
  onModeChange: (mode: "signin" | "signup") => void
  onSuccess?: () => void
}

export function GoogleAuth({ isOpen, onClose, mode, onModeChange, onSuccess }: GoogleAuthProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showMagicLink, setShowMagicLink] = useState(false)
  const [magicLinkSent, setMagicLinkSent] = useState(false)
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    phone: "",
    subscribeAnnouncements: false,
  })

  const { signIn, signUp, signInWithGoogle, signInWithMagicLink, resetPassword } = useAuth()

  const resetForm = () => {
    setFormData({
      email: "",
      password: "",
      confirmPassword: "",
      firstName: "",
      lastName: "",
      phone: "",
      subscribeAnnouncements: false,
    })
    setShowPassword(false)
    setShowConfirmPassword(false)
    setShowMagicLink(false)
    setMagicLinkSent(false)
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    try {
      const result = await signInWithGoogle()
      if (result.success) {
        toast.success('Redirecting to Google...')
        onSuccess?.()
        handleClose()
      } else {
        toast.error(result.error || 'Failed to sign in with Google')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault()

    if (mode === 'signup') {
      // Validation for signup
      if (!formData.firstName.trim() || !formData.lastName.trim()) {
        toast.error('Please enter your first and last name')
        return
      }

      if (formData.password !== formData.confirmPassword) {
        toast.error('Passwords do not match')
        return
      }

      if (formData.password.length < 6) {
        toast.error('Password must be at least 6 characters')
        return
      }
    }

    setIsLoading(true)
    try {
      let result
      if (mode === 'signin') {
        result = await signIn(formData.email, formData.password)
      } else {
        result = await signUp(
          formData.email,
          formData.password,
          formData.firstName,
          formData.lastName,
          formData.phone,
          formData.subscribeAnnouncements
        )
      }

      if (result.success) {
        if (mode === 'signup') {
          toast.success('Account created! Please check your email to verify your account.')
        } else {
          toast.success('Welcome back!')
        }
        onSuccess?.()
        handleClose()
      } else {
        toast.error(result.error || 'Authentication failed')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleMagicLink = async () => {
    if (!formData.email) {
      toast.error('Please enter your email address first')
      return
    }

    setIsLoading(true)
    try {
      const result = await signInWithMagicLink(formData.email)
      if (result.success) {
        setMagicLinkSent(true)
        toast.success('Magic link sent! Check your email.')
      } else {
        toast.error(result.error || 'Failed to send magic link')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleForgotPassword = async () => {
    if (!formData.email) {
      toast.error('Please enter your email address first')
      return
    }

    setIsLoading(true)
    try {
      const result = await resetPassword(formData.email)
      if (result.success) {
        toast.success('Password reset link sent! Check your email.')
      } else {
        toast.error(result.error || 'Failed to send reset link')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  if (magicLinkSent) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="bg-white max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-bold text-pink-800">
              Check Your Email
            </DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto">
              <Mail className="h-8 w-8 text-pink-600" />
            </div>
            <p className="text-gray-600">
              We've sent a magic link to <strong>{formData.email}</strong>
            </p>
            <p className="text-sm text-gray-500">
              Click the link in your email to sign in securely.
            </p>
            <Button
              onClick={() => setMagicLinkSent(false)}
              variant="outline"
              className="border-pink-300 text-pink-700"
            >
              Back to Sign In
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-white max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-bold text-pink-800">
            {mode === "signin" ? "Welcome Back!" : "Join Luxe Nails"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Google Sign In Button */}
          <Button
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className="w-full h-12 bg-white border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors"
          >
            {isLoading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-700 mr-3"></div>
                Connecting...
              </div>
            ) : (
              <>
                <GoogleIcon />
                Continue with Google
              </>
            )}
          </Button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white px-2 text-gray-500">Or continue with email</span>
            </div>
          </div>

          {/* Email Form */}
          <form onSubmit={handleEmailAuth} className="space-y-4">
            {mode === "signup" && (
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="firstName" className="text-pink-800">
                    First Name
                  </Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                      className="pl-10 border-pink-200 focus:border-pink-500"
                      placeholder="John"
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="lastName" className="text-pink-800">
                    Last Name
                  </Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                      className="pl-10 border-pink-200 focus:border-pink-500"
                      placeholder="Doe"
                      required
                    />
                  </div>
                </div>
              </div>
            )}

            <div>
              <Label htmlFor="email" className="text-pink-800">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="pl-10 border-pink-200 focus:border-pink-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="password" className="text-pink-800">
                Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="pl-10 pr-10 border-pink-200 focus:border-pink-500"
                  placeholder="••••••••"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {mode === "signup" && (
              <>
                <div>
                  <Label htmlFor="confirmPassword" className="text-pink-800">
                    Verify Password
                  </Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                      className="pl-10 pr-10 border-pink-200 focus:border-pink-500"
                      placeholder="••••••••"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="phone" className="text-pink-800">
                    Phone Number (Optional)
                  </Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      className="pl-10 border-pink-200 focus:border-pink-500"
                      placeholder="(*************"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="subscribeAnnouncements"
                    checked={formData.subscribeAnnouncements}
                    onCheckedChange={(checked) =>
                      setFormData({ ...formData, subscribeAnnouncements: checked as boolean })
                    }
                  />
                  <Label htmlFor="subscribeAnnouncements" className="text-sm text-pink-700">
                    Subscribe to announcements by email
                  </Label>
                </div>
              </>
            )}

            <Button
              type="submit"
              disabled={isLoading || !formData.email || !formData.password}
              className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {mode === "signin" ? "Signing in..." : "Creating account..."}
                </div>
              ) : (
                <>{mode === "signin" ? "Sign In" : "Create Account"}</>
              )}
            </Button>
          </form>

          {/* Magic Link and Forgot Password */}
          {mode === "signin" && formData.email && (
            <div className="space-y-2">
              <Button
                type="button"
                onClick={handleMagicLink}
                disabled={isLoading}
                variant="outline"
                className="w-full border-pink-300 text-pink-700 hover:bg-pink-50"
              >
                <Send className="h-4 w-4 mr-2" />
                Send Magic Link
              </Button>

              <Button
                type="button"
                onClick={handleForgotPassword}
                disabled={isLoading}
                variant="ghost"
                className="w-full text-pink-600 hover:text-pink-700 hover:bg-pink-50"
              >
                Forgot Password?
              </Button>
            </div>
          )}

          {/* Toggle Mode */}
          <div className="text-center">
            <p className="text-sm text-gray-600">
              {mode === "signin" ? "Don't have an account?" : "Already have an account?"}{" "}
              <button
                type="button"
                onClick={() => {
                  onModeChange(mode === "signin" ? "signup" : "signin")
                  resetForm()
                }}
                className="text-pink-600 hover:text-pink-700 font-semibold"
              >
                {mode === "signin" ? "Sign up" : "Sign in"}
              </button>
            </p>
          </div>

          {mode === "signup" && (
            <div className="text-xs text-gray-500 text-center">
              By creating an account, you agree to our{" "}
              <a href="#" className="text-pink-600 hover:underline">
                Terms of Service
              </a>{" "}
              and{" "}
              <a href="#" className="text-pink-600 hover:underline">
                Privacy Policy
              </a>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
