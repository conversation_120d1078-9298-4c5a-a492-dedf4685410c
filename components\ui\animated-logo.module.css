@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  50% {
    transform: translateX(100%) skewX(-15deg);
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
  }
}

@keyframes metallicPulse {
  0%, 100% {
    filter: hue-rotate(0deg) brightness(1);
  }
  25% {
    filter: hue-rotate(10deg) brightness(1.1);
  }
  50% {
    filter: hue-rotate(-10deg) brightness(0.9);
  }
  75% {
    filter: hue-rotate(5deg) brightness(1.05);
  }
}

@keyframes streaks {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

.shimmer {
  animation: shimmer 3s ease-in-out infinite;
  transform: translateX(-100%);
}

.metallicPulse {
  animation: metallicPulse 4s ease-in-out infinite;
}

.streaks {
  animation: streaks 6s linear infinite;
}
