"use client"

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Plus, Upload, Star, Heart, Eye, EyeOff, Trash2, Edit, Camera, X, Users, CheckCircle, Clock, Home } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface NailImage {
  id: string
  url?: string
  image_url?: string
  service_name: string
  client_name: string | null
  date_created: string
  featured: boolean
  public: boolean
  likes_count: number
  review_id: string | null
  review_text: string | null
  review_rating: number | null
  user_id: string | null
  created_at: string
  is_private: boolean
  approved_for_homepage: boolean
  users?: {
    first_name: string
    last_name: string
  }
}

export function AdminMyPortfolio() {
  const [adminImages, setAdminImages] = useState<NailImage[]>([])
  const [clientImages, setClientImages] = useState<NailImage[]>([])
  const [loading, setLoading] = useState(true)
  const [clientLoading, setClientLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [isUploadOpen, setIsUploadOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [formData, setFormData] = useState({
    service_name: '',
    client_name: '',
    public: true
  })

  useEffect(() => {
    fetchAdminImages()
    fetchClientImages()
  }, [])

  const fetchAdminImages = async () => {
    try {
      const { data, error } = await supabase
        .from('nail_images')
        .select('*')
        .is('user_id', null) // Admin uploaded images
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching admin images:', error)
        toast.error('Failed to load admin images')
        return
      }

      const formattedImages = (data || []).map((image: any) => ({
        id: image.id,
        url: image.url || image.image_url,
        service_name: image.service_name,
        client_name: image.client_name,
        date_created: image.created_at,
        featured: image.featured || false,
        public: image.public,
        likes_count: image.likes_count || 0,
        review_id: null,
        review_text: null,
        review_rating: null,
        user_id: image.user_id,
        created_at: image.created_at,
        is_private: image.is_private || false,
        approved_for_homepage: image.approved_for_homepage || false
      }))

      setAdminImages(formattedImages)
    } catch (error) {
      console.error('Error fetching admin images:', error)
      toast.error('Failed to load admin images')
    } finally {
      setLoading(false)
    }
  }

  const fetchClientImages = async () => {
    try {
      const { data, error } = await supabase
        .from('nail_images')
        .select(`
          *,
          users!nail_images_user_id_fkey (
            first_name,
            last_name
          )
        `)
        .not('user_id', 'is', null) // Client uploaded images
        .eq('is_private', false) // Only non-private images
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching client images:', error)
        return
      }

      const formattedImages = (data || []).map((image: any) => ({
        id: image.id,
        url: image.url || image.image_url,
        service_name: `${image.users?.first_name || 'Client'}'s Work`,
        client_name: `${image.users?.first_name || ''} ${image.users?.last_name || ''}`.trim(),
        date_created: image.created_at,
        featured: false,
        public: true,
        likes_count: image.likes_count || 0,
        review_id: null,
        review_text: null,
        review_rating: null,
        user_id: image.user_id,
        created_at: image.created_at,
        is_private: image.is_private || false,
        approved_for_homepage: image.approved_for_homepage || false,
        users: image.users
      }))

      setClientImages(formattedImages)
    } catch (error) {
      console.error('Error fetching client images:', error)
    } finally {
      setClientLoading(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image must be less than 5MB')
      return
    }

    setSelectedFile(file)
    
    // Create preview URL
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
  }

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedFile || !formData.service_name.trim()) {
      toast.error('Please select an image and enter service name')
      return
    }

    setUploading(true)
    try {
      // Upload image to Supabase Storage
      const fileExt = selectedFile.name.split('.').pop()
      const fileName = `admin/${Date.now()}.${fileExt}`
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('nail-images')
        .upload(fileName, selectedFile)

      if (uploadError) {
        console.error('Upload error:', uploadError)
        toast.error('Failed to upload image')
        return
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('nail-images')
        .getPublicUrl(fileName)

      // Save image record to database
      const { error } = await supabase
        .from('nail_images')
        .insert({
          url: publicUrl,
          service_name: formData.service_name.trim(),
          client_name: formData.client_name.trim() || null,
          public: formData.public,
          user_id: null, // Admin uploaded
          is_private: false,
          approved_for_homepage: false
        })

      if (error) {
        console.error('Error saving image record:', error)
        toast.error('Failed to save image')
        return
      }

      toast.success('Image uploaded successfully!')
      setFormData({
        service_name: '',
        client_name: '',
        public: true
      })
      setSelectedFile(null)
      setPreviewUrl(null)
      setIsUploadOpen(false)
      fetchAdminImages()
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image')
    } finally {
      setUploading(false)
    }
  }

  const toggleHomepageApproval = async (imageId: string, currentApproval: boolean) => {
    try {
      const { error } = await supabase
        .from('nail_images')
        .update({ approved_for_homepage: !currentApproval })
        .eq('id', imageId)

      if (error) {
        console.error('Error toggling homepage approval:', error)
        toast.error('Failed to update image')
        return
      }

      toast.success(`Image ${!currentApproval ? 'approved for' : 'removed from'} homepage`)
      fetchAdminImages()
      fetchClientImages()
    } catch (error) {
      console.error('Error toggling homepage approval:', error)
      toast.error('Failed to update image')
    }
  }

  const togglePublic = async (imageId: string, currentPublic: boolean) => {
    try {
      const { error } = await supabase
        .from('nail_images')
        .update({ public: !currentPublic })
        .eq('id', imageId)

      if (error) {
        console.error('Error toggling public:', error)
        toast.error('Failed to update image')
        return
      }

      toast.success(`Image ${!currentPublic ? 'made public' : 'made private'}`)
      fetchAdminImages()
    } catch (error) {
      console.error('Error toggling public:', error)
      toast.error('Failed to update image')
    }
  }

  const deleteImage = async (imageId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) return

    try {
      const { error } = await supabase
        .from('nail_images')
        .delete()
        .eq('id', imageId)

      if (error) {
        console.error('Error deleting image:', error)
        toast.error('Failed to delete image')
        return
      }

      toast.success('Image deleted successfully!')
      fetchAdminImages()
      fetchClientImages()
    } catch (error) {
      console.error('Error deleting image:', error)
      toast.error('Failed to delete image')
    }
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6 max-w-none w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">My Portfolio</h2>
          <p className="text-gray-600 dark:text-gray-400">Manage your portfolio and approve client content for homepage display</p>
        </div>
        <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              <Camera className="h-4 w-4 mr-2" />
              Upload Image
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle className="text-pink-800 dark:text-pink-300">Upload New Image</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleUpload} className="space-y-4">
              {/* File Upload Area */}
              <div
                className="border-2 border-dashed border-pink-300 rounded-lg p-6 text-center cursor-pointer hover:border-pink-400 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                {previewUrl ? (
                  <div className="relative">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        setSelectedFile(null)
                        setPreviewUrl(null)
                      }}
                      className="absolute top-2 right-2 bg-white"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <div>
                    <Upload className="h-12 w-12 text-pink-300 mx-auto mb-4" />
                    <p className="text-pink-600 mb-2">Click to upload or drag and drop</p>
                    <p className="text-sm text-gray-500">PNG, JPG up to 5MB</p>
                  </div>
                )}
                <Input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>

              <div>
                <Label htmlFor="service_name" className="text-pink-800 dark:text-pink-300">
                  Service Name *
                </Label>
                <Input
                  id="service_name"
                  value={formData.service_name}
                  onChange={(e) => setFormData({ ...formData, service_name: e.target.value })}
                  placeholder="e.g., Gel Manicure with Art"
                  className="border-pink-200 dark:border-pink-700"
                  required
                />
              </div>

              <div>
                <Label htmlFor="client_name" className="text-pink-800 dark:text-pink-300">
                  Client Name (Optional)
                </Label>
                <Input
                  id="client_name"
                  value={formData.client_name}
                  onChange={(e) => setFormData({ ...formData, client_name: e.target.value })}
                  placeholder="Client's name"
                  className="border-pink-200 dark:border-pink-700"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={formData.public}
                  onCheckedChange={(checked) => setFormData({ ...formData, public: checked })}
                />
                <Label className="text-pink-800 dark:text-pink-300">Public</Label>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsUploadOpen(false)}
                  className="border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={uploading}
                  className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  {uploading ? 'Uploading...' : 'Upload'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Tabs for Admin Work and Client Content */}
      <Tabs defaultValue="admin-work" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="admin-work">My Work</TabsTrigger>
          <TabsTrigger value="client-content">
            <Users className="h-4 w-4 mr-2" />
            Client Content
          </TabsTrigger>
        </TabsList>

        {/* Admin Work Tab */}
        <TabsContent value="admin-work" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {adminImages.map((image) => (
              <Card key={image.id} className="border-pink-200 dark:border-pink-700 overflow-hidden">
                <div className="relative">
                  <img
                    src={image.url}
                    alt={image.service_name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 left-2 flex flex-col space-y-1">
                    {image.public ? (
                      <Badge className="bg-green-500 text-white">
                        <Eye className="h-3 w-3 mr-1" />
                        Public
                      </Badge>
                    ) : (
                      <Badge className="bg-gray-500 text-white">
                        <EyeOff className="h-3 w-3 mr-1" />
                        Private
                      </Badge>
                    )}
                    {image.approved_for_homepage && (
                      <Badge className="bg-blue-500 text-white">
                        <Home className="h-3 w-3 mr-1" />
                        Homepage
                      </Badge>
                    )}
                  </div>
                  <div className="absolute top-2 right-2 flex flex-col space-y-1">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => toggleHomepageApproval(image.id, image.approved_for_homepage)}
                      className={`bg-white/90 ${
                        image.approved_for_homepage
                          ? 'border-blue-500 text-blue-700 hover:bg-blue-50'
                          : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <Home className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => togglePublic(image.id, image.public)}
                      className="bg-white/90 border-blue-300 text-blue-700 hover:bg-blue-50"
                    >
                      {image.public ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => deleteImage(image.id)}
                      className="bg-white/90 border-red-300 text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">{image.service_name}</h3>
                  {image.client_name && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Client: {image.client_name}</p>
                  )}
                  <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">
                    {new Date(image.date_created).toLocaleDateString()}
                  </p>

                  <div className="flex items-center justify-between mt-3">
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Heart className="h-4 w-4 mr-1" />
                      {image.likes_count} likes
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {adminImages.length === 0 && (
            <Card className="border-pink-200 dark:border-pink-700">
              <CardContent className="p-6 text-center">
                <Upload className="h-12 w-12 text-pink-300 dark:text-pink-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-pink-800 dark:text-pink-300 mb-2">No Images Yet</h3>
                <p className="text-pink-600 dark:text-pink-400">Upload your first nail art image to get started!</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Client Content Tab */}
        <TabsContent value="client-content" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {clientImages.map((image) => (
              <Card key={image.id} className="border-pink-200 dark:border-pink-700 overflow-hidden">
                <div className="relative">
                  <img
                    src={image.url}
                    alt={image.service_name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 left-2 flex flex-col space-y-1">
                    <Badge className="bg-blue-500 text-white">
                      <Users className="h-3 w-3 mr-1" />
                      Client
                    </Badge>
                    {image.approved_for_homepage ? (
                      <Badge className="bg-green-500 text-white">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Approved
                      </Badge>
                    ) : (
                      <Badge className="bg-amber-500 text-white">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    )}
                  </div>
                  <div className="absolute top-2 right-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => toggleHomepageApproval(image.id, image.approved_for_homepage)}
                      className={`bg-white/90 ${
                        image.approved_for_homepage
                          ? 'border-green-500 text-green-700 hover:bg-green-50'
                          : 'border-amber-500 text-amber-700 hover:bg-amber-50'
                      }`}
                    >
                      {image.approved_for_homepage ? <CheckCircle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">{image.service_name}</h3>
                  {image.client_name && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">By: {image.client_name}</p>
                  )}
                  <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">
                    {new Date(image.date_created).toLocaleDateString()}
                  </p>

                  <div className="flex items-center justify-between mt-3">
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Heart className="h-4 w-4 mr-1" />
                      {image.likes_count} likes
                    </div>
                    <div className="text-xs text-gray-500">
                      {image.approved_for_homepage ? 'Approved for homepage' : 'Awaiting approval'}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {clientImages.length === 0 && (
            <Card className="border-pink-200 dark:border-pink-700">
              <CardContent className="p-6 text-center">
                <Users className="h-12 w-12 text-pink-300 dark:text-pink-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-pink-800 dark:text-pink-300 mb-2">No Client Content Yet</h3>
                <p className="text-pink-600 dark:text-pink-400">Client uploaded images will appear here when they share their work!</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
