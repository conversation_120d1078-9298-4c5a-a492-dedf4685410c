"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface TipSelectorProps {
  servicePrice: number
  onTipChange: (tip: number) => void
}

export function TipSelector({ servicePrice, onTipChange }: TipSelectorProps) {
  const [selectedTip, setSelectedTip] = useState<number>(0)
  const [customTip, setCustomTip] = useState("")
  const [tipType, setTipType] = useState<"percentage" | "amount">("percentage")

  const tipPercentages = [15, 18, 20, 25]

  const handlePercentageTip = (percentage: number) => {
    const tipAmount = (servicePrice * percentage) / 100
    setSelectedTip(tipAmount)
    setCustomTip("")
    setTipType("percentage")
    onTipChange(tipAmount)
  }

  const handleCustomTip = (amount: string) => {
    const tipAmount = Number.parseFloat(amount) || 0
    setCustomTip(amount)
    setSelectedTip(tipAmount)
    setTipType("amount")
    onTipChange(tipAmount)
  }

  const handleNoTip = () => {
    setSelectedTip(0)
    setCustomTip("")
    onTipChange(0)
  }

  return (
    <div className="space-y-4">
      <Label className="text-base font-semibold text-gray-900">Add a tip for your nail artist</Label>

      {/* Percentage Tips */}
      <div className="grid grid-cols-2 gap-3">
        {tipPercentages.map((percentage) => {
          const tipAmount = (servicePrice * percentage) / 100
          const isSelected = tipType === "percentage" && selectedTip === tipAmount
          return (
            <Button
              key={percentage}
              variant={isSelected ? "default" : "outline"}
              className={`h-16 flex flex-col ${
                isSelected
                  ? "bg-gradient-to-r from-pink-500 to-amber-500 text-white"
                  : "border-pink-200 hover:bg-pink-50"
              }`}
              onClick={() => handlePercentageTip(percentage)}
            >
              <span className="text-lg font-bold">{percentage}%</span>
              <span className="text-sm opacity-80">${tipAmount.toFixed(2)}</span>
            </Button>
          )
        })}
      </div>

      {/* Custom Amount */}
      <div className="space-y-2">
        <Label htmlFor="customTip" className="text-sm text-gray-700">
          Custom amount
        </Label>
        <div className="relative">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
          <Input
            id="customTip"
            type="number"
            placeholder="0.00"
            value={customTip}
            onChange={(e) => handleCustomTip(e.target.value)}
            className="pl-8 border-pink-200 focus:border-pink-500"
            step="0.01"
            min="0"
          />
        </div>
      </div>

      {/* No Tip Option */}
      <Button variant="ghost" className="w-full text-gray-600 hover:bg-gray-50" onClick={handleNoTip}>
        No tip
      </Button>

      {/* Total */}
      {selectedTip > 0 && (
        <div className="bg-pink-50 p-4 rounded-lg border border-pink-200">
          <div className="flex justify-between items-center">
            <span className="text-gray-700">Service total:</span>
            <span className="font-semibold">${servicePrice.toFixed(2)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-700">Tip:</span>
            <span className="font-semibold text-pink-600">+${selectedTip.toFixed(2)}</span>
          </div>
          <div className="border-t border-pink-200 mt-2 pt-2 flex justify-between items-center">
            <span className="text-lg font-bold text-gray-900">Total:</span>
            <span className="text-lg font-bold text-pink-600">${(servicePrice + selectedTip).toFixed(2)}</span>
          </div>
        </div>
      )}
    </div>
  )
}
