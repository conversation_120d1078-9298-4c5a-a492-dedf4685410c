import { NextRequest, NextResponse } from 'next/server'
import { sendEmail, createEmailVerificationTemplate } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    // Test email template
    const template = createEmailVerificationTemplate(
      email,
      'https://example.com/verify'
    )

    const result = await sendEmail(template)

    if (!result.success) {
      return NextResponse.json({ 
        error: 'Failed to send test email', 
        details: result.error 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Test email sent successfully',
      data: result.data 
    })

  } catch (error) {
    console.error('Test email error:', error)
    return NextResponse.json({ error: 'Test email failed', details: error }, { status: 500 })
  }
}
