import { Resend } from 'resend'

const resend = new Resend(process.env.RESEND_API_KEY)

export interface EmailTemplate {
  to: string
  subject: string
  html: string
}

// Email verification template
export const createEmailVerificationTemplate = (
  email: string,
  verificationUrl: string
): EmailTemplate => ({
  to: email,
  subject: 'Verify your email - Nails by Lingg',
  html: `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email</title>
      <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; }
        .header { background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 32px; font-family: 'Dancing Script', cursive; }
        .content { padding: 40px 20px; }
        .button { display: inline-block; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Nails by Lingg</h1>
          <p style="color: #fce7f3; margin: 10px 0 0 0;">Premium Nail Care & Artistry</p>
        </div>
        <div class="content">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Welcome to Nails by Lingg!</h2>
          <p style="color: #4b5563; line-height: 1.6;">Thank you for joining our community of nail art enthusiasts. To complete your registration and start booking appointments, please verify your email address.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </div>
          <p style="color: #6b7280; font-size: 14px;">If you didn't create an account with us, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>© 2024 Nails by Lingg. All rights reserved.</p>
          <p>123 Beauty Lane, Luxury City | (555) 123-NAIL</p>
        </div>
      </div>
    </body>
    </html>
  `
})

// Magic link template
export const createMagicLinkTemplate = (
  email: string,
  magicLinkUrl: string
): EmailTemplate => ({
  to: email,
  subject: 'Your magic link - Nails by Lingg',
  html: `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Magic Link</title>
      <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; }
        .header { background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 32px; font-family: 'Dancing Script', cursive; }
        .content { padding: 40px 20px; }
        .button { display: inline-block; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Nails by Lingg</h1>
          <p style="color: #fce7f3; margin: 10px 0 0 0;">Premium Nail Care & Artistry</p>
        </div>
        <div class="content">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Sign in to your account</h2>
          <p style="color: #4b5563; line-height: 1.6;">Click the button below to securely sign in to your Nails by Lingg account. This link will expire in 1 hour.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${magicLinkUrl}" class="button">Sign In Securely</a>
          </div>
          <p style="color: #6b7280; font-size: 14px;">If you didn't request this sign-in link, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>© 2024 Nails by Lingg. All rights reserved.</p>
          <p>123 Beauty Lane, Luxury City | (555) 123-NAIL</p>
        </div>
      </div>
    </body>
    </html>
  `
})

// Password reset template
export const createPasswordResetTemplate = (
  email: string,
  resetUrl: string
): EmailTemplate => ({
  to: email,
  subject: 'Reset your password - Nails by Lingg',
  html: `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password</title>
      <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; }
        .header { background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 32px; font-family: 'Dancing Script', cursive; }
        .content { padding: 40px 20px; }
        .button { display: inline-block; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Nails by Lingg</h1>
          <p style="color: #fce7f3; margin: 10px 0 0 0;">Premium Nail Care & Artistry</p>
        </div>
        <div class="content">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Reset your password</h2>
          <p style="color: #4b5563; line-height: 1.6;">We received a request to reset your password. Click the button below to create a new password. This link will expire in 1 hour.</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" class="button">Reset Password</a>
          </div>
          <p style="color: #6b7280; font-size: 14px;">If you didn't request a password reset, you can safely ignore this email.</p>
        </div>
        <div class="footer">
          <p>© 2024 Nails by Lingg. All rights reserved.</p>
          <p>123 Beauty Lane, Luxury City | (555) 123-NAIL</p>
        </div>
      </div>
    </body>
    </html>
  `
})

// Appointment confirmation template
export const createAppointmentConfirmationTemplate = (
  email: string,
  appointmentDetails: {
    date: string
    time: string
    services: string[]
    totalPrice: number
    duration: number
  }
): EmailTemplate => ({
  to: email,
  subject: 'Appointment Confirmed - Nails by Lingg',
  html: `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Appointment Confirmed</title>
      <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; }
        .header { background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 32px; font-family: 'Dancing Script', cursive; }
        .content { padding: 40px 20px; }
        .appointment-card { background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Nails by Lingg</h1>
          <p style="color: #fce7f3; margin: 10px 0 0 0;">Premium Nail Care & Artistry</p>
        </div>
        <div class="content">
          <h2 style="color: #1f2937; margin-bottom: 20px;">Your appointment is confirmed!</h2>
          <p style="color: #4b5563; line-height: 1.6;">We're excited to see you soon! Here are your appointment details:</p>
          
          <div class="appointment-card">
            <h3 style="color: #1f2937; margin-top: 0;">Appointment Details</h3>
            <p><strong>Date:</strong> ${appointmentDetails.date}</p>
            <p><strong>Time:</strong> ${appointmentDetails.time}</p>
            <p><strong>Services:</strong> ${appointmentDetails.services.join(', ')}</p>
            <p><strong>Duration:</strong> ${Math.floor(appointmentDetails.duration / 60)}h ${appointmentDetails.duration % 60}m</p>
            <p><strong>Total:</strong> $${appointmentDetails.totalPrice}</p>
          </div>
          
          <p style="color: #4b5563; line-height: 1.6;">Please arrive 10 minutes early for your appointment. If you need to reschedule or cancel, please contact us at least 24 hours in advance.</p>
        </div>
        <div class="footer">
          <p>© 2024 Nails by Lingg. All rights reserved.</p>
          <p>123 Beauty Lane, Luxury City | (555) 123-NAIL</p>
        </div>
      </div>
    </body>
    </html>
  `
})

// Send email function
export const sendEmail = async (template: EmailTemplate) => {
  try {
    const { data, error } = await resend.emails.send({
      from: 'Nails by Lingg <<EMAIL>>',
      to: template.to,
      subject: template.subject,
      html: template.html,
    })

    if (error) {
      console.error('Error sending email:', error)
      return { success: false, error }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error sending email:', error)
    return { success: false, error }
  }
}

// Send announcement email to subscribers
export const sendAnnouncementEmail = async (
  subscribers: string[],
  title: string,
  content: string
) => {
  const template: EmailTemplate = {
    to: subscribers.join(','),
    subject: `${title} - Nails by Lingg`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body { font-family: 'Arial', sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); }
          .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; }
          .header { background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); padding: 40px 20px; text-align: center; }
          .header h1 { color: white; margin: 0; font-size: 32px; font-family: 'Dancing Script', cursive; }
          .content { padding: 40px 20px; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Nails by Lingg</h1>
            <p style="color: #fce7f3; margin: 10px 0 0 0;">Premium Nail Care & Artistry</p>
          </div>
          <div class="content">
            <h2 style="color: #1f2937; margin-bottom: 20px;">${title}</h2>
            <div style="color: #4b5563; line-height: 1.6;">${content}</div>
          </div>
          <div class="footer">
            <p>© 2024 Nails by Lingg. All rights reserved.</p>
            <p>123 Beauty Lane, Luxury City | (555) 123-NAIL</p>
            <p><a href="#" style="color: #6b7280;">Unsubscribe from announcements</a></p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  return sendEmail(template)
}

// Referral invitation template
export const createReferralInvitationTemplate = (
  email: string,
  senderName: string,
  referralCode: string
): EmailTemplate => ({
  to: email,
  subject: `${senderName} invited you to Nails by Lingg - 20% off your first visit!`,
  html: `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>You're Invited to Nails by Lingg</title>
      <style>
        body { font-family: 'Arial', sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; }
        .header { background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); padding: 40px 20px; text-align: center; }
        .header h1 { color: white; margin: 0; font-size: 32px; font-family: 'Dancing Script', cursive; }
        .content { padding: 40px 30px; }
        .button { display: inline-block; background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); color: white; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: bold; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6b7280; font-size: 14px; }
        .discount-box { background: linear-gradient(135deg, #ec4899 0%, #f59e0b 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .referral-code { background: #f3f4f6; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; border: 2px dashed #ec4899; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Nails by Lingg</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 18px;">You're Invited!</p>
        </div>
        <div class="content">
          <h2 style="color: #ec4899; margin-bottom: 20px;">Hi there!</h2>
          <p style="color: #4b5563; line-height: 1.6; font-size: 16px;">Your friend <strong>${senderName}</strong> thinks you'd love the luxury nail experience at Nails by Lingg!</p>

          <div class="discount-box">
            <h3 style="margin: 0 0 10px 0; font-size: 24px;">🎉 Special Invitation Offer</h3>
            <p style="margin: 0; font-size: 18px; opacity: 0.9;">Get 20% off your first appointment!</p>
          </div>

          <p style="color: #4b5563; line-height: 1.6;">We're a premium nail salon specializing in luxury manicures, intricate nail art, and spa treatments. Our expert technicians use only the finest products to ensure you leave feeling absolutely radiant.</p>

          <div class="referral-code">
            <p style="margin: 0 0 10px 0; color: #6b7280; font-size: 14px;">Use referral code:</p>
            <p style="margin: 0; font-size: 24px; font-weight: bold; color: #ec4899; letter-spacing: 2px;">${referralCode}</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://nailsbylingg.com'}/booking?ref=${referralCode}" class="button">Book Your Appointment</a>
          </div>

          <p style="color: #6b7280; font-size: 14px; text-align: center;">This offer is valid for new customers only. Cannot be combined with other offers.</p>
        </div>
        <div class="footer">
          <p>© 2024 Nails by Lingg. All rights reserved.</p>
          <p>123 Beauty Lane, Luxury City | (555) 123-NAIL</p>
          <p style="margin-top: 15px;">
            <a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://nailsbylingg.com'}" style="color: #ec4899; text-decoration: none;">Visit our website</a> |
            <a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://nailsbylingg.com'}/services" style="color: #ec4899; text-decoration: none;">View Services</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `
})
