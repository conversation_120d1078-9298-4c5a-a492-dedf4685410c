"use client"

import type React from "react"
import Link from "next/link"
import {
  <PERSON><PERSON>,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
} from "@/components/ui/sidebar"
import { BarChart3, Calendar, Clock, Crown, Eye, Gift, Home, ImageIcon, Scissors, Settings, Users, FileText, Star, Building, User, Megaphone, Package } from "lucide-react"

// Navigation items for the admin sidebar
const adminNavItems = [
  {
    title: "Overview",
    items: [
      {
        title: "Dashboard",
        icon: BarChart3,
        value: "dashboard",
        description: "Analytics and overview",
      },
      {
        title: "Appointments",
        icon: Calendar,
        value: "appointments",
        description: "Manage bookings",
      },
    ],
  },
  {
    title: "Business Management",
    items: [
      {
        title: "Availability",
        icon: Clock,
        value: "availability",
        description: "Hours and blocked times",
      },
      {
        title: "Services",
        icon: Scissors,
        value: "services",
        description: "Manage service offerings",
      },
    ],
  },
  {
    title: "Content Management",
    items: [
      {
        title: "Homepage Stats",
        icon: BarChart3,
        value: "homepage-stats",
        description: "Quick stats display",
      },
      {
        title: "Customer Reviews",
        icon: Star,
        value: "customer-reviews",
        description: "Manage testimonials",
      },
      {
        title: "Business Info",
        icon: Building,
        value: "business-info",
        description: "Contact details",
      },

      {
        title: "Owner Profile",
        icon: User,
        value: "owner-info",
        description: "About the owner",
      },
      {
        title: "Booking Policies",
        icon: FileText,
        value: "booking-policies",
        description: "Terms & conditions",
      },
    ],
  },
  {
    title: "Service Management",
    items: [
      {
        title: "Services",
        icon: Scissors,
        value: "services",
        description: "Manage services",
      },
      {
        title: "Service Combinations",
        icon: Package,
        value: "service-combinations",
        description: "Bundle discounts",
      },

      {
        title: "Schedule",
        icon: Clock,
        value: "schedule",
        description: "Manage availability",
      },
    ],
  },
  {
    title: "Customer Management",
    items: [
      {
        title: "Clients",
        icon: Users,
        value: "users",
        description: "Customer database",
      },
      {
        title: "My Work",
        icon: ImageIcon,
        value: "my-work",
        description: "Portfolio management",
      },
      {
        title: "Portfolio",
        icon: ImageIcon,
        value: "portfolio",
        description: "Photo gallery",
      },
      {
        title: "Referrals",
        icon: Gift,
        value: "referrals",
        description: "Referral program",
      },
      {
        title: "Announcements",
        icon: Megaphone,
        value: "announcements",
        description: "Email campaigns",
      },
    ],
  },
]

interface AdminSidebarProps {
  selectedTab: string
  onTabChange: (tab: string) => void
}

export function AdminSidebar({ selectedTab, onTabChange }: AdminSidebarProps) {
  return (
    <Sidebar variant="inset" className="border-pink-200 dark:border-gray-700 bg-white dark:bg-gray-900 z-50">
      <SidebarHeader className="border-b border-pink-200 dark:border-gray-700 bg-gradient-to-r from-pink-50 to-amber-50 dark:from-gray-800 dark:to-gray-800 sticky top-0 z-50">
        <div className="flex items-center gap-2 px-4 py-3">
          <Crown className="h-6 w-6 text-amber-500" />
          <div className="flex flex-col">
            <span
              className="text-lg font-bold bg-gradient-to-r from-pink-600 to-amber-500 bg-clip-text text-transparent"
              style={{ fontFamily: "Dancing Script, cursive" }}
            >
              Admin Panel
            </span>
            <span className="text-xs text-pink-600 dark:text-pink-400">Nails by Lingg</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="bg-white dark:bg-gray-900">
        {adminNavItems.map((group) => (
          <SidebarGroup key={group.title}>
            <SidebarGroupLabel className="text-pink-800 dark:text-pink-300 font-semibold">{group.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => {
                  const Icon = item.icon
                  const isActive = selectedTab === item.value
                  return (
                    <SidebarMenuItem key={item.value}>
                      <SidebarMenuButton
                        onClick={() => onTabChange(item.value)}
                        isActive={isActive}
                        className={`w-full justify-start ${
                          isActive
                            ? "bg-gradient-to-r from-pink-500 to-amber-500 text-white hover:from-pink-600 hover:to-amber-600"
                            : "text-pink-700 dark:text-pink-300 hover:bg-pink-50 dark:hover:bg-gray-800"
                        }`}
                      >
                        <Icon className="h-4 w-4" />
                        <div className="flex flex-col items-start">
                          <span className="font-medium">{item.title}</span>
                          <span className="text-xs opacity-70">{item.description}</span>
                        </div>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter className="border-t border-pink-200 bg-white">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="text-pink-700 hover:bg-pink-100">
              <Link href="/">
                <Home className="h-4 w-4" />
                <span>Back to App</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton className="text-pink-700 hover:bg-pink-100">
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  )
}

export function AdminSidebarProvider({ children }: { children: React.ReactNode }) {
  return <SidebarProvider>{children}</SidebarProvider>
}
