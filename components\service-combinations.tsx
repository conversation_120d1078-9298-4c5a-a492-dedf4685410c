"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, Clock, DollarSign, Package, ChevronRight } from 'lucide-react'
import { supabase } from '@/lib/supabase'

interface Service {
  id: string
  name: string
  price: number
  duration: number
  category: string
}

interface ServiceCombination {
  id: string
  name: string
  description: string
  service_ids: string[]
  discount_type: 'percentage' | 'fixed'
  discount_value: number
  original_price: number
  discounted_price: number
  total_duration: number
  featured: boolean
  active: boolean
  services?: Service[]
}

interface ServiceCombinationsProps {
  showTitle?: boolean
  limit?: number
  featuredOnly?: boolean
  onSelectCombination?: (combination: ServiceCombination) => void
  selectedServiceIds?: string[]
}

export function ServiceCombinations({ 
  showTitle = true, 
  limit, 
  featuredOnly = false,
  onSelectCombination,
  selectedServiceIds = []
}: ServiceCombinationsProps) {
  const [combinations, setCombinations] = useState<ServiceCombination[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCombinations()
    fetchServices()
  }, [limit, featuredOnly])

  const fetchCombinations = async () => {
    try {
      let query = supabase
        .from('service_combinations')
        .select('*')
        .eq('active', true)
        .order('featured', { ascending: false })
        .order('created_at', { ascending: false })

      if (featuredOnly) {
        query = query.eq('featured', true)
      }

      if (limit) {
        query = query.limit(limit)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching combinations:', error)
        return
      }

      setCombinations(data || [])
    } catch (error) {
      console.error('Error fetching combinations:', error)
    }
  }

  const fetchServices = async () => {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('active', true)

      if (error) {
        console.error('Error fetching services:', error)
        return
      }

      setServices(data || [])
    } catch (error) {
      console.error('Error fetching services:', error)
    } finally {
      setLoading(false)
    }
  }

  const getServicesForCombination = (serviceIds: string[]) => {
    return services.filter(service => serviceIds.includes(service.id))
  }

  const isSelected = (combination: ServiceCombination) => {
    return combination.service_ids.every(id => selectedServiceIds.includes(id)) &&
           selectedServiceIds.every(id => combination.service_ids.includes(id))
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {showTitle && <div className="h-6 bg-gray-200 rounded animate-pulse" />}
        <div className="space-y-3">
          {[...Array(limit || 3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  if (combinations.length === 0) {
    return null
  }

  return (
    <div className="space-y-4">
      {showTitle && (
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">Popular Combinations</h2>
          <Button variant="ghost" size="sm" className="text-pink-600">
            View All
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      )}

      <div className="space-y-3">
        {combinations.map((combination) => {
          const combinationServices = getServicesForCombination(combination.service_ids)
          const savings = combination.original_price - combination.discounted_price
          const savingsPercentage = (savings / combination.original_price) * 100
          const selected = isSelected(combination)

          return (
            <Card 
              key={combination.id} 
              className={`border-pink-200 bg-white/80 backdrop-blur-sm cursor-pointer transition-all hover:shadow-md ${
                selected ? 'ring-2 ring-pink-500 bg-pink-50' : ''
              }`}
              onClick={() => onSelectCombination?.(combination)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <CardTitle className="text-lg text-pink-800">{combination.name}</CardTitle>
                      {combination.featured && (
                        <Badge className="bg-amber-100 text-amber-800">
                          <Star className="h-3 w-3 mr-1" />
                          Popular
                        </Badge>
                      )}
                      {selected && (
                        <Badge className="bg-pink-100 text-pink-800">
                          Selected
                        </Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm">{combination.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-pink-800">${combination.discounted_price.toFixed(2)}</div>
                    <div className="text-sm text-gray-500 line-through">${combination.original_price.toFixed(2)}</div>
                    <div className="text-xs text-green-600 font-medium">Save {savingsPercentage.toFixed(0)}%</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 text-sm">Includes:</h4>
                    <div className="flex flex-wrap gap-1">
                      {combinationServices.map((service) => (
                        <Badge key={service.id} variant="outline" className="border-pink-300 text-pink-700 text-xs">
                          {service.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {Math.floor(combination.total_duration / 60)}h {combination.total_duration % 60}m
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-1" />
                      Save ${savings.toFixed(2)}
                    </div>
                  </div>

                  {onSelectCombination && (
                    <Button 
                      className={`w-full ${
                        selected 
                          ? 'bg-pink-600 hover:bg-pink-700' 
                          : 'bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation()
                        onSelectCombination(combination)
                      }}
                    >
                      <Package className="h-4 w-4 mr-2" />
                      {selected ? 'Selected' : 'Select This Package'}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
