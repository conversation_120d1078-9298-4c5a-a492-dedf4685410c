-- Insert default services
INSERT INTO public.services (name, description, price, duration, category, active) VALUES
('Classic Manicure', 'Traditional nail care with cuticle work, shaping, and polish application', 35.00, 45, 'Classic', true),
('Gel Manicure', 'Long-lasting gel polish application with base and top coat', 45.00, 60, 'Classic', true),
('Luxury Spa Manicure', 'Premium spa experience with exfoliation, massage, and luxury treatments', 65.00, 75, 'Classic', true),
('Simple Nail Art', 'Basic nail art designs including stripes, dots, and simple patterns', 55.00, 60, 'Art', true),
('Intricate Nail Art', 'Complex artistic designs with detailed patterns and multiple colors', 75.00, 90, 'Art', true),
('Gel Extensions', 'Gel nail extensions for length and strength', 70.00, 90, 'Extensions', true),
('Diamond Manicure', 'Ultimate luxury experience with premium products and diamond dust finish', 120.00, 90, 'Luxury', true),
('Classic Pedicure', 'Complete foot care with soak, exfoliation, and polish', 40.00, 50, 'Pedicure', true),
('Spa Pedicure', 'Luxury pedicure with extended massage and premium treatments', 60.00, 70, 'Pedicure', true);

-- Insert default business hours
INSERT INTO public.business_hours (day_of_week, open_time, close_time, is_closed) VALUES
(0, '10:00', '17:00', false), -- Sunday
(1, '09:00', '19:00', false), -- Monday
(2, '09:00', '19:00', false), -- Tuesday
(3, '09:00', '19:00', false), -- Wednesday
(4, '09:00', '19:00', false), -- Thursday
(5, '09:00', '19:00', false), -- Friday
(6, '09:00', '19:00', false); -- Saturday

-- Create storage bucket for nail images
INSERT INTO storage.buckets (id, name, public) VALUES ('nail-images', 'nail-images', true);

-- Create storage policy for nail images
CREATE POLICY "Anyone can view nail images" ON storage.objects
  FOR SELECT USING (bucket_id = 'nail-images');

CREATE POLICY "Authenticated users can upload nail images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'nail-images' AND 
    auth.role() = 'authenticated'
  );

CREATE POLICY "Users can update their own nail images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'nail-images' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own nail images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'nail-images' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );
