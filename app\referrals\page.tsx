"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AppHeader } from "@/components/ui/app-header"
import { useAuth } from "@/components/auth/auth-provider"
import { GoogleAuth } from "@/components/auth/google-auth"
import { Gift, Users, Mail, CheckCircle, Copy, Share, User, Edit3 } from "lucide-react"
import { toast } from "sonner"
import { supabase } from "@/lib/supabase"

export default function ReferralsPage() {
  const { user, isSignedIn } = useAuth()
  const [emails, setEmails] = useState([""])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [referralCode, setReferralCode] = useState("")
  const [customCode, setCustomCode] = useState("")
  const [isEditingCode, setIsEditingCode] = useState(false)
  const [authOpen, setAuthOpen] = useState(false)
  const [authMode, setAuthMode] = useState<"signin" | "signup">("signin")
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (isSignedIn && user) {
      loadUserReferralCode()
    }
    setLoading(false)
  }, [isSignedIn, user])

  const loadUserReferralCode = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('users')
        .select('referral_code')
        .eq('id', user.id)
        .single()

      if (error) {
        console.error('Error loading referral code:', error)
        // Generate a default code if none exists
        const defaultCode = `${user.first_name?.toUpperCase() || 'USER'}${Math.random().toString(36).substr(2, 4).toUpperCase()}`
        setReferralCode(defaultCode)
        return
      }

      if (data.referral_code) {
        setReferralCode(data.referral_code)
      } else {
        // Generate a default code if none exists
        const defaultCode = `${user.first_name?.toUpperCase() || 'USER'}${Math.random().toString(36).substr(2, 4).toUpperCase()}`
        setReferralCode(defaultCode)
        // Save it to the database
        await updateReferralCode(defaultCode)
      }
    } catch (error) {
      console.error('Error loading referral code:', error)
    }
  }

  const updateReferralCode = async (code: string) => {
    if (!user) return

    try {
      const { error } = await supabase
        .from('users')
        .update({ referral_code: code })
        .eq('id', user.id)

      if (error) {
        console.error('Error updating referral code:', error)
        toast.error('Failed to update referral code')
        return false
      }

      return true
    } catch (error) {
      console.error('Error updating referral code:', error)
      toast.error('Failed to update referral code')
      return false
    }
  }

  const validateReferralCode = (code: string): boolean => {
    // Basic validation: alphanumeric only, at least 1 character
    const regex = /^[A-Z0-9]+$/
    return regex.test(code) && code.length > 0
  }

  const handleSaveCustomCode = async () => {
    if (!customCode.trim()) {
      toast.error('Please enter a referral code')
      return
    }

    const upperCode = customCode.toUpperCase().trim()

    if (!validateReferralCode(upperCode)) {
      toast.error('Referral code must contain only letters and numbers')
      return
    }

    // Check if referral code already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id')
      .eq('referral_code', upperCode)
      .neq('id', user?.id)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking referral code:', checkError)
      toast.error('Failed to validate referral code')
      return
    }

    if (existingUser) {
      toast.error('This referral code is already in use. Please choose a different one.')
      return
    }

    // Check if code is already taken
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .eq('referral_code', upperCode)
        .neq('id', user?.id)

      if (error) {
        console.error('Error checking referral code:', error)
        toast.error('Failed to validate referral code')
        return
      }

      if (data && data.length > 0) {
        toast.error('This referral code is already taken. Please choose another.')
        return
      }

      // Update the code
      const success = await updateReferralCode(upperCode)
      if (success) {
        setReferralCode(upperCode)
        setIsEditingCode(false)
        setCustomCode("")
        toast.success('Referral code updated successfully!')
      }
    } catch (error) {
      console.error('Error saving referral code:', error)
      toast.error('Failed to save referral code')
    }
  }

  const addEmailField = () => {
    if (emails.length < 5) {
      setEmails([...emails, ""])
    }
  }

  const updateEmail = (index: number, value: string) => {
    const newEmails = [...emails]
    newEmails[index] = value
    setEmails(newEmails)
  }

  const removeEmail = (index: number) => {
    if (emails.length > 1) {
      const newEmails = emails.filter((_, i) => i !== index)
      setEmails(newEmails)
    }
  }

  const handleSubmit = async () => {
    if (!isSignedIn) {
      setAuthMode("signin")
      setAuthOpen(true)
      return
    }

    const validEmails = emails.filter((email) => email.trim() && email.includes("@"))
    if (validEmails.length === 0) {
      toast.error('Please enter at least one valid email address')
      return
    }

    setIsSubmitting(true)

    try {
      // Send referral invitations via API
      const response = await fetch('/api/send-referral-invitations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emails: validEmails,
          referralCode: referralCode,
          senderName: `${user?.first_name} ${user?.last_name}`,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send invitations')
      }

      toast.success(`Invitations sent to ${validEmails.length} friends!`)
      setSubmitted(true)
    } catch (error) {
      console.error('Error sending invitations:', error)
      toast.error('Failed to send invitations. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const copyReferralCode = () => {
    navigator.clipboard.writeText(referralCode)
    toast.success('Referral code copied to clipboard!')
  }

  const shareReferral = () => {
    if (navigator.share) {
      navigator.share({
        title: "Nails by Lingg",
        text: `Check out Nails by Lingg! Use my referral code ${referralCode} for 20% off your first appointment.`,
        url: window.location.origin,
      })
    } else {
      // Fallback for browsers that don't support Web Share API
      copyReferralCode()
    }
  }

  const handleAuthSuccess = () => {
    setAuthOpen(false)
  }

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <AppHeader title="Refer Friends" showBack />
        <div className="p-4 flex items-center justify-center min-h-[70vh]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"></div>
        </div>
      </div>
    )
  }

  // Show authentication required state
  if (!isSignedIn) {
    return (
      <>
        <div className="min-h-screen bg-gray-50 pb-20">
          <AppHeader title="Refer Friends" showBack />
          <div className="p-4 flex items-center justify-center min-h-[70vh]">
            <Card className="border-pink-200 bg-white/80 backdrop-blur-sm max-w-sm">
              <CardContent className="p-6 text-center">
                <User className="h-12 w-12 text-pink-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-pink-800 mb-2">Sign In Required</h3>
                <p className="text-pink-600 mb-4">Please sign in to access the referral program and start earning rewards.</p>
                <Button
                  onClick={() => {
                    setAuthMode("signin")
                    setAuthOpen(true)
                  }}
                  className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  Sign In
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        <GoogleAuth
          isOpen={authOpen}
          onClose={() => setAuthOpen(false)}
          mode={authMode}
          onModeChange={setAuthMode}
          onSuccess={handleAuthSuccess}
        />
      </>
    )
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <AppHeader title="Referrals Sent!" showBack />

        <div className="p-4 flex items-center justify-center min-h-[70vh]">
          <Card className="border-0 shadow-lg text-center max-w-sm">
            <CardContent className="p-8">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Invitations Sent!</h2>
              <p className="text-gray-600 mb-6">
                Your friends will receive an email invitation with a special 20% off discount. You'll earn rewards when
                they book their first appointment!
              </p>

              <div className="bg-pink-50 p-4 rounded-lg mb-6 border border-pink-200">
                <p className="text-pink-700 font-semibold">You'll earn $10 credit for each friend who books!</p>
              </div>

              <Button
                onClick={() => setSubmitted(false)}
                className="w-full bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600 mb-3"
              >
                Refer More Friends
              </Button>
              <Button variant="outline" onClick={() => window.history.back()} className="w-full border-pink-200">
                Back to App
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <AppHeader title="Refer Friends" showBack />

      <div className="p-4 space-y-6">
        {/* Rewards Info */}
        <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-500 to-amber-500 text-white">
          <CardContent className="p-6 text-center">
            <Gift className="h-12 w-12 mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">Earn $10 Per Referral!</h2>
            <p className="text-pink-100 mb-4">
              Share the luxury with friends and earn $10 credit for each friend who books their first appointment.
            </p>
            <div className="bg-white/20 rounded-lg p-3">
              <p className="text-sm">Your friends get 20% off their first visit!</p>
            </div>
          </CardContent>
        </Card>

        {/* Your Referral Code */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Your Referral Code</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsEditingCode(!isEditingCode)
                  setCustomCode(referralCode)
                }}
                className="text-pink-600 hover:text-pink-700"
              >
                <Edit3 className="h-4 w-4" />
              </Button>
            </div>

            {isEditingCode ? (
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Input
                    value={customCode}
                    onChange={(e) => setCustomCode(e.target.value.toUpperCase())}
                    placeholder="Enter custom code"
                    className="flex-1 border-pink-200 focus:border-pink-500"
                  />
                  <Button
                    size="sm"
                    onClick={handleSaveCustomCode}
                    className="bg-pink-500 hover:bg-pink-600"
                  >
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsEditingCode(false)
                      setCustomCode("")
                    }}
                    className="border-gray-300"
                  >
                    Cancel
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  Letters and numbers only. We'll check for inappropriate content automatically.
                </p>
              </div>
            ) : (
              <>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-100 p-3 rounded-lg">
                    <code className="text-lg font-bold text-pink-600">{referralCode}</code>
                  </div>
                  <Button variant="outline" size="sm" onClick={copyReferralCode} className="border-pink-200">
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={shareReferral} className="border-pink-200">
                    <Share className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Share this code or use the form below to send invitations. Click the edit icon to customize your code.
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Email Invitations */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">Send Email Invitations</h3>
              <Users className="h-5 w-5 text-pink-500" />
            </div>

            <div className="space-y-3">
              {emails.map((email, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="flex-1">
                    <Label htmlFor={`email-${index}`} className="sr-only">
                      Email {index + 1}
                    </Label>
                    <Input
                      id={`email-${index}`}
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => updateEmail(index, e.target.value)}
                      className="border-pink-200 focus:border-pink-500"
                    />
                  </div>
                  {emails.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEmail(index)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      ×
                    </Button>
                  )}
                </div>
              ))}
            </div>

            {emails.length < 5 && (
              <Button variant="outline" onClick={addEmailField} className="w-full border-pink-200 text-pink-700">
                + Add Another Email
              </Button>
            )}

            <div className="bg-pink-50 p-3 rounded-lg border border-pink-200">
              <p className="text-sm text-pink-700">
                <Mail className="h-4 w-4 inline mr-1" />
                We'll send a beautiful invitation email with your personal referral code and their 20% discount.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Current Referrals */}
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Your Referral Stats</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-pink-600">3</div>
                <div className="text-xs text-gray-500">Invited</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">1</div>
                <div className="text-xs text-gray-500">Booked</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-amber-600">$10</div>
                <div className="text-xs text-gray-500">Earned</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Send Button */}
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || emails.every((email) => !email.trim())}
          className="w-full py-3 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600 disabled:opacity-50"
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Sending Invitations...
            </div>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" />
              Send Invitations
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
