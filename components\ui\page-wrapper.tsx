"use client"

import { usePathname } from "next/navigation"
import { AppHeader } from "@/components/ui/app-header"

interface PageWrapperProps {
  children: React.ReactNode
}

export function PageWrapper({ children }: PageWrapperProps) {
  const pathname = usePathname()

  // Admin pages should use full width but still have header
  if (pathname.startsWith("/admin")) {
    return (
      <div className="w-full bg-white dark:bg-gray-900 min-h-screen">
        <AppHeader title="Admin Dashboard" showNotifications />
        {children}
      </div>
    )
  }

  // Other pages use mobile-first container
  return (
    <div className="w-full max-w-md mx-auto bg-white dark:bg-gray-900 min-h-screen relative md:max-w-full md:px-4 lg:px-8">
      <div className="md:max-w-4xl md:mx-auto">
        {children}
      </div>
    </div>
  )
}
