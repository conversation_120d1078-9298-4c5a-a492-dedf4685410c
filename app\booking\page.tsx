"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar } from "@/components/ui/calendar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { ServiceCombinations } from "@/components/service-combinations"
import { BookingCalendar } from "@/components/booking-calendar"
import { PaymentForm } from "@/components/payment-form"
import { AppHeader } from "@/components/ui/app-header"
import { useAuth } from "@/components/auth/auth-provider"
import { supabase } from "@/lib/supabase"
import { CreditCard, Clock, ArrowLeft, ArrowRight, Package, CheckCircle } from "lucide-react"
import { toast } from "sonner"

interface Service {
  id: string
  name: string
  price: number
  duration: number
  category: string
  description?: string
  image_url?: string
  active: boolean
}

interface ServiceCombination {
  id: string
  name: string
  description: string
  service_ids: string[]
  discount_type: 'percentage' | 'fixed'
  discount_value: number
  original_price: number
  discounted_price: number
  total_duration: number
  featured: boolean
  active: boolean
}

// Moved timeSlots and bookedSlots logic to BookingCalendar component

export default function BookingPage() {
  const { user, isSignedIn } = useAuth()
  const [step, setStep] = useState(1)
  const [services, setServices] = useState<Service[]>([])
  const [combinations, setCombinations] = useState<ServiceCombination[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedServices, setSelectedServices] = useState<string[]>([])
  const [selectedCombination, setSelectedCombination] = useState<ServiceCombination | null>(null)
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [selectedTime, setSelectedTime] = useState("")
  const [customerInfo, setCustomerInfo] = useState({
    firstName: user?.first_name || "",
    lastName: user?.last_name || "",
    email: user?.email || "",
    phone: user?.phone || "",
    notes: "",
  })
  const [reminderDays, setReminderDays] = useState(1)
  const [paymentLoading, setPaymentLoading] = useState(false)

  useEffect(() => {
    fetchServices()
    fetchCombinations()
  }, [])

  useEffect(() => {
    if (user) {
      setCustomerInfo({
        firstName: user.first_name || "",
        lastName: user.last_name || "",
        email: user.email || "",
        phone: user.phone || "",
        notes: "",
      })
    }
  }, [user])

  const fetchServices = async () => {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('active', true)
        .order('category', { ascending: true })
        .order('name', { ascending: true })

      if (error) {
        console.error('Error fetching services:', error)
        toast.error('Failed to load services')
        return
      }

      setServices(data || [])
    } catch (error) {
      console.error('Error fetching services:', error)
      toast.error('Failed to load services')
    }
  }

  const fetchCombinations = async () => {
    try {
      const { data, error } = await supabase
        .from('service_combinations')
        .select('*')
        .eq('active', true)
        .order('featured', { ascending: false })
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching combinations:', error)
        toast.error('Failed to load service combinations')
        return
      }

      setCombinations(data || [])
    } catch (error) {
      console.error('Error fetching combinations:', error)
      toast.error('Failed to load service combinations')
    } finally {
      setLoading(false)
    }
  }

  const selectedServiceData = services.filter((s) => selectedServices.includes(s.id))

  // Calculate totals
  const subtotal = selectedCombination
    ? selectedCombination.original_price
    : selectedServiceData.reduce((sum, service) => sum + service.price, 0)

  const totalDuration = selectedCombination
    ? selectedCombination.total_duration
    : selectedServiceData.reduce((sum, service) => sum + service.duration, 0)

  // Apply combination discount if applicable
  const combinationDiscountAmount = selectedCombination
    ? selectedCombination.original_price - selectedCombination.discounted_price
    : 0

  // Member discount (applied after combination discount)
  const memberDiscount = isSignedIn && user?.is_member ? 0.15 : 0
  const discountedSubtotal = selectedCombination ? selectedCombination.discounted_price : subtotal
  const memberDiscountAmount = discountedSubtotal * memberDiscount

  const total = discountedSubtotal - memberDiscountAmount

  const toggleService = (serviceId: string) => {
    // Clear combination selection when manually selecting services
    setSelectedCombination(null)

    const newServices = selectedServices.includes(serviceId)
      ? selectedServices.filter((id) => id !== serviceId)
      : [...selectedServices, serviceId]

    setSelectedServices(newServices)
  }

  const handleServiceCardClick = (serviceId: string) => {
    toggleService(serviceId)
  }

  const handleCombinationSelect = (combination: ServiceCombination) => {
    setSelectedCombination(combination)
    setSelectedServices(combination.service_ids)
  }

  // Time slot availability logic moved to BookingCalendar component

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '')

    // Format as (XXX) XXX-XXXX
    if (digits.length <= 3) {
      return digits
    } else if (digits.length <= 6) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3)}`
    } else {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`
    }
  }

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePhone = (phone: string) => {
    const phoneRegex = /^\(\d{3}\) \d{3}-\d{4}$/
    return phoneRegex.test(phone)
  }

  const nextStep = () => {
    if (step === 1 && selectedServices.length === 0) {
      toast.error("Please select at least one service")
      return
    }
    if (step === 2 && (!selectedDate || !selectedTime)) {
      toast.error("Please select date and time")
      return
    }
    if (step === 3) {
      if (!customerInfo.firstName.trim() || !customerInfo.lastName.trim()) {
        toast.error("Please enter your first and last name")
        return
      }
      if (!customerInfo.email.trim() || !validateEmail(customerInfo.email)) {
        toast.error("Please enter a valid email address")
        return
      }
      if (!customerInfo.phone.trim() || !validatePhone(customerInfo.phone)) {
        toast.error("Please enter a valid phone number in format (XXX) XXX-XXXX")
        return
      }
    }
    setStep(step + 1)
  }

  const prevStep = () => {
    setStep(step - 1)
  }

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value)
    setCustomerInfo({ ...customerInfo, phone: formatted })
  }

  const handlePayment = async (paymentData: any) => {
    setPaymentLoading(true)
    try {
      // In a real app, you would process the payment with Stripe or another payment processor
      // For now, we'll simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Create booking in database after successful payment
      const bookingData = {
        user_id: user?.id || null,
        service_ids: selectedServices,
        combination_id: selectedCombination?.id || null,
        appointment_date: selectedDate?.toISOString().split('T')[0],
        appointment_time: selectedTime,
        customer_name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        special_requests: customerInfo.notes || null,
        total_price: total,
        total_duration: totalDuration,
        reminder_days_before: reminderDays,
        status: 'confirmed',
        payment_status: 'paid'
      }

      // In a real app, you would save this to the database
      console.log('Booking data:', bookingData)
      console.log('Payment data:', paymentData)

      toast.success("Payment successful! Your appointment is confirmed.")
      setStep(6) // Move to confirmation step
    } catch (error) {
      console.error('Payment error:', error)
      toast.error("Payment failed. Please try again.")
    } finally {
      setPaymentLoading(false)
    }
  }

  const handleBooking = async () => {
    try {
      // Create booking in database
      const bookingData = {
        user_id: user?.id || null,
        service_ids: selectedServices,
        combination_id: selectedCombination?.id || null,
        appointment_date: selectedDate?.toISOString().split('T')[0],
        appointment_time: selectedTime,
        customer_name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        special_requests: customerInfo.notes || null,
        total_price: total,
        total_duration: totalDuration,
        reminder_days_before: reminderDays,
        status: 'pending'
      }

      // In a real app, you would save this to the database
      console.log('Booking data:', bookingData)

      toast.success("Booking confirmed! You will receive a confirmation email shortly.")

      // Reset form or redirect
      setStep(1)
      setSelectedServices([])
      setSelectedCombination(null)
      setSelectedDate(undefined)
      setSelectedTime("")
      setCustomerInfo({
        firstName: user?.first_name || "",
        lastName: user?.last_name || "",
        email: user?.email || "",
        phone: user?.phone || "",
        notes: "",
      })
    } catch (error) {
      console.error('Booking error:', error)
      toast.error("Failed to create booking. Please try again.")
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <AppHeader title="Book Appointment" showBack />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="space-y-4">
            <div className="h-8 bg-gray-200 rounded animate-pulse" />
            <div className="h-64 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <AppHeader title="Book Appointment" showBack />
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h1
            className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-pink-600 to-amber-500 bg-clip-text text-transparent"
            style={{ fontFamily: "Dancing Script, cursive" }}
          >
            Book Your Appointment
          </h1>
          <p className="text-lg text-pink-800">Schedule your luxury nail experience</p>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-2">
            {[1, 2, 3, 4, 5].map((stepNum) => (
              <div key={stepNum} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                    step >= stepNum ? "bg-gradient-to-r from-pink-500 to-amber-500" : "bg-gray-300"
                  }`}
                >
                  {stepNum}
                </div>
                {stepNum < 5 && (
                  <div
                    className={`w-8 h-1 ${step > stepNum ? "bg-gradient-to-r from-pink-500 to-amber-500" : "bg-gray-300"}`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Description */}
        <div className="text-center mb-6">
          <div className="text-sm text-gray-600">
            {step <= 5 ? `Step ${step} of 5: ` : "Complete! "}
            {step === 1 && "Select Services"}
            {step === 2 && "Choose Date & Time"}
            {step === 3 && "Your Information"}
            {step === 4 && "Review & Confirm"}
            {step === 5 && "Payment"}
            {step === 6 && "Booking Confirmed"}
          </div>
        </div>

        <Card className="border-pink-200 bg-white/80 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-2xl text-pink-800" style={{ fontFamily: "Dancing Script, cursive" }}>
              {step === 1 && "Select Your Services"}
              {step === 2 && "Choose Date & Time"}
              {step === 3 && "Your Information"}
              {step === 4 && "Confirm & Pay"}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            {/* Step 1: Service Selection */}
            {step === 1 && (
              <div className="space-y-6">
                {/* Service Combinations */}
                {combinations.length > 0 && (
                  <div>
                    <ServiceCombinations
                      showTitle={true}
                      featuredOnly={false}
                      onSelectCombination={handleCombinationSelect}
                      selectedServiceIds={selectedServices}
                    />
                  </div>
                )}

                {combinations.length > 0 && <Separator />}

                {/* Individual Services */}
                <div>
                  <h3 className="text-lg font-semibold text-pink-800 mb-4">
                    {combinations.length > 0 ? "Or Choose Individual Services" : "Choose Your Services"}
                  </h3>

                  {/* Group services by category */}
                  {Array.from(new Set(services.map(s => s.category))).map((category) => {
                    const categoryServices = services.filter((service) => service.category === category)
                    if (categoryServices.length === 0) return null

                    return (
                      <div key={category} className="mb-6">
                        <h4 className="font-medium text-gray-900 mb-3">{category} Services</h4>
                        <div className="grid md:grid-cols-2 gap-3">
                          {categoryServices.map((service) => (
                            <Card
                              key={service.id}
                              className={`cursor-pointer transition-all hover:shadow-md ${
                                selectedServices.includes(service.id)
                                  ? "ring-2 ring-pink-500 bg-pink-50"
                                  : "border-pink-200"
                              }`}
                              onClick={() => handleServiceCardClick(service.id)}
                            >
                              <CardContent className="p-4">
                                <div className="flex items-start justify-between">
                                  <div className="flex items-start space-x-3">
                                    <Checkbox
                                      checked={selectedServices.includes(service.id)}
                                      onCheckedChange={() => toggleService(service.id)}
                                      className="mt-1"
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    <div className="flex-1">
                                      <h4 className="font-semibold text-pink-800">{service.name}</h4>
                                      {service.description && (
                                        <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                                      )}
                                      <div className="flex items-center space-x-4 mt-2">
                                        <span className="text-lg font-bold text-amber-600">${service.price}</span>
                                        <Badge variant="secondary" className="bg-pink-100 text-pink-700">
                                          <Clock className="h-3 w-3 mr-1" />
                                          {service.duration} min
                                        </Badge>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>

                {/* Selection Summary */}
                {selectedServices.length > 0 && (
                  <Card className="bg-pink-50 border-pink-200">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-pink-800">Your Selection</h4>
                        {selectedCombination && (
                          <Badge className="bg-amber-100 text-amber-800">
                            <Package className="h-3 w-3 mr-1" />
                            {selectedCombination.name}
                          </Badge>
                        )}
                      </div>

                      <div className="space-y-2 mb-4">
                        {selectedServiceData.map((service) => (
                          <div key={service.id} className="flex justify-between items-center">
                            <span className="text-gray-700">{service.name}</span>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-gray-500">{service.duration} min</span>
                              <span className="font-semibold">${service.price}</span>
                            </div>
                          </div>
                        ))}
                      </div>

                      <Separator className="my-3" />

                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">Subtotal:</span>
                          <span className="font-semibold">${subtotal.toFixed(2)}</span>
                        </div>

                        {combinationDiscountAmount > 0 && (
                          <div className="flex justify-between items-center text-green-600">
                            <span>Combination Discount:</span>
                            <span>-${combinationDiscountAmount.toFixed(2)}</span>
                          </div>
                        )}

                        {memberDiscountAmount > 0 && (
                          <div className="flex justify-between items-center text-green-600">
                            <span>Member Discount (15%):</span>
                            <span>-${memberDiscountAmount.toFixed(2)}</span>
                          </div>
                        )}

                        <Separator className="my-2" />

                        <div className="flex justify-between items-center">
                          <div>
                            <span className="font-bold text-lg text-pink-800">
                              Total: ${total.toFixed(2)}
                            </span>
                            <div className="text-sm text-gray-600">
                              <Clock className="h-4 w-4 inline mr-1" />
                              {Math.floor(totalDuration / 60)}h {totalDuration % 60}m
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Step 2: Date & Time Selection */}
            {step === 2 && (
              <BookingCalendar
                selectedDate={selectedDate}
                selectedTime={selectedTime}
                totalDuration={totalDuration}
                onDateSelect={setSelectedDate}
                onTimeSelect={setSelectedTime}
              />
            )}

            {/* Step 3: Customer Information */}
            {step === 3 && (
              <div className="space-y-6">
                {!isSignedIn && (
                  <div className="flex items-center justify-between p-4 bg-pink-50 rounded-lg border border-pink-200">
                    <div>
                      <h3 className="font-semibold text-pink-800">Sign in for exclusive benefits!</h3>
                      <p className="text-sm text-pink-600">Get 15% member discount and access to exclusive promos</p>
                    </div>
                    <Button
                      onClick={() => window.location.href = '/auth'}
                      variant="outline"
                      className="border-pink-300 text-pink-700"
                    >
                      Sign In
                    </Button>
                  </div>
                )}

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName" className="text-pink-800">
                      First Name
                    </Label>
                    <Input
                      id="firstName"
                      value={customerInfo.firstName}
                      onChange={(e) => setCustomerInfo({ ...customerInfo, firstName: e.target.value })}
                      className="border-pink-200 focus:border-pink-500"
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName" className="text-pink-800">
                      Last Name
                    </Label>
                    <Input
                      id="lastName"
                      value={customerInfo.lastName}
                      onChange={(e) => setCustomerInfo({ ...customerInfo, lastName: e.target.value })}
                      className="border-pink-200 focus:border-pink-500"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="email" className="text-pink-800">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={customerInfo.email}
                    onChange={(e) => setCustomerInfo({ ...customerInfo, email: e.target.value })}
                    className="border-pink-200 focus:border-pink-500"
                  />
                </div>

                <div>
                  <Label htmlFor="phone" className="text-pink-800">
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={customerInfo.phone}
                    onChange={(e) => handlePhoneChange(e.target.value)}
                    className="border-pink-200 focus:border-pink-500"
                    placeholder="(*************"
                  />
                </div>

                <div>
                  <Label htmlFor="notes" className="text-pink-800">
                    Special Requests (Optional)
                  </Label>
                  <Textarea
                    id="notes"
                    value={customerInfo.notes}
                    onChange={(e) => setCustomerInfo({ ...customerInfo, notes: e.target.value })}
                    className="border-pink-200 focus:border-pink-500"
                    placeholder="Any special requests, color preferences, or notes for your nail artist..."
                  />
                </div>

                <div className="bg-pink-50 p-4 rounded-lg border border-pink-200">
                  <h4 className="font-semibold text-pink-800 mb-3">Appointment Reminders</h4>
                  <div className="space-y-2">
                    <Label className="text-pink-700">Send me a reminder:</Label>
                    <div className="flex space-x-4">
                      {[1, 2, 3].map((days) => (
                        <label key={days} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="radio"
                            name="reminderDays"
                            value={days}
                            checked={reminderDays === days}
                            onChange={(e) => setReminderDays(parseInt(e.target.value))}
                            className="text-pink-600"
                          />
                          <span className="text-sm text-gray-700">
                            {days} day{days > 1 ? 's' : ''} before
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Confirmation */}
            {step === 4 && (
              <div className="space-y-6">
                <div className="bg-pink-50 p-6 rounded-lg border border-pink-200">
                  <h3 className="text-xl font-semibold text-pink-800 mb-4">Booking Summary</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-pink-700">Customer:</span>
                      <span className="font-semibold text-pink-800">
                        {customerInfo.firstName} {customerInfo.lastName}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-pink-700">Email:</span>
                      <span className="font-semibold text-pink-800">{customerInfo.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-pink-700">Phone:</span>
                      <span className="font-semibold text-pink-800">{customerInfo.phone}</span>
                    </div>

                    <Separator />

                    <div className="flex justify-between">
                      <span className="text-pink-700">Services:</span>
                      <div className="text-right">
                        {selectedCombination ? (
                          <div className="font-semibold text-pink-800">
                            {selectedCombination.name} (Package)
                          </div>
                        ) : (
                          selectedServiceData.map((service) => (
                            <div key={service.id} className="font-semibold text-pink-800">
                              {service.name}
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-pink-700">Date:</span>
                      <span className="font-semibold text-pink-800">{selectedDate?.toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-pink-700">Time:</span>
                      <span className="font-semibold text-pink-800">{selectedTime}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-pink-700">Duration:</span>
                      <span className="font-semibold text-pink-800">
                        {Math.floor(totalDuration / 60)}h {totalDuration % 60}m
                      </span>
                    </div>

                    <Separator />

                    <div className="flex justify-between">
                      <span className="text-pink-700">Subtotal:</span>
                      <span className="font-semibold text-pink-800">${subtotal.toFixed(2)}</span>
                    </div>
                    {combinationDiscountAmount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Package Discount:</span>
                        <span>-${combinationDiscountAmount.toFixed(2)}</span>
                      </div>
                    )}
                    {memberDiscountAmount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Member Discount (15%):</span>
                        <span>-${memberDiscountAmount.toFixed(2)}</span>
                      </div>
                    )}

                    <Separator />

                    <div className="flex justify-between text-lg font-bold">
                      <span className="text-pink-800">Total:</span>
                      <span className="text-amber-600">${total.toFixed(2)}</span>
                    </div>

                    {customerInfo.notes && (
                      <>
                        <Separator />
                        <div>
                          <span className="text-pink-700 font-medium">Special Requests:</span>
                          <p className="text-gray-700 mt-1">{customerInfo.notes}</p>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-pink-200">
                  <h3 className="text-xl font-semibold text-pink-800 mb-4">Payment Information</h3>
                  <div className="space-y-4">
                    <div>
                      <Label className="text-pink-800">Card Number</Label>
                      <Input placeholder="1234 5678 9012 3456" className="border-pink-200" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-pink-800">Expiry Date</Label>
                        <Input placeholder="MM/YY" className="border-pink-200" />
                      </div>
                      <div>
                        <Label className="text-pink-800">CVV</Label>
                        <Input placeholder="123" className="border-pink-200" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 5: Payment */}
            {step === 5 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-pink-800 mb-2">Complete Your Payment</h2>
                  <p className="text-gray-600">Secure payment to confirm your appointment</p>
                </div>

                <PaymentForm
                  total={total}
                  onPaymentSubmit={handlePayment}
                  loading={paymentLoading}
                />
              </div>
            )}

            {/* Step 6: Final Confirmation */}
            {step === 6 && (
              <div className="text-center space-y-6">
                <div className="bg-green-50 p-8 rounded-lg border border-green-200">
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-green-800 mb-2">Booking Confirmed!</h2>
                  <p className="text-green-700 mb-4">
                    Your appointment has been successfully booked and payment processed.
                  </p>

                  <div className="bg-white p-4 rounded-lg border border-green-200 text-left">
                    <h3 className="font-semibold text-green-800 mb-2">Appointment Details:</h3>
                    <div className="space-y-1 text-sm text-gray-700">
                      <div>Date: {selectedDate?.toLocaleDateString()}</div>
                      <div>Time: {selectedTime}</div>
                      <div>Duration: {Math.floor(totalDuration / 60)}h {totalDuration % 60}m</div>
                      <div>Total: ${total.toFixed(2)}</div>
                    </div>
                  </div>

                  <div className="mt-6 space-y-2 text-sm text-green-700">
                    <p>✓ Confirmation email sent to {customerInfo.email}</p>
                    <p>✓ Reminder set for {reminderDays} day{reminderDays > 1 ? 's' : ''} before</p>
                    <p>✓ Payment processed successfully</p>
                  </div>
                </div>

                <div className="flex space-x-4 justify-center">
                  <Button
                    onClick={() => window.location.href = '/'}
                    className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                  >
                    Return Home
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/appointments'}
                    className="border-pink-300 text-pink-700"
                  >
                    View My Appointments
                  </Button>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            {step < 6 && (
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  disabled={step === 1 || step === 5}
                  className="border-pink-300 text-pink-700 hover:bg-pink-50"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>

                {step < 4 ? (
                  <Button
                    onClick={nextStep}
                    className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                  >
                    Next
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : step === 4 ? (
                  <Button
                    onClick={nextStep}
                    className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                  >
                    Proceed to Payment
                    <CreditCard className="ml-2 h-4 w-4" />
                  </Button>
                ) : null}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
