import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Clock, CreditCard, Calendar, Shield, AlertCircle, Heart } from "lucide-react"

const policies = [
  {
    title: "Booking & Scheduling",
    icon: <Calendar className="h-6 w-6" />,
    content: [
      "Appointments can be booked online 24/7 or by phone during business hours",
      "We recommend booking at least 48 hours in advance for availability",
      "Same-day appointments may be available based on schedule",
      "Group bookings (3+ people) require 72-hour advance notice",
      "Special occasion bookings should be made 1-2 weeks in advance",
    ],
  },
  {
    title: "Cancellation Policy",
    icon: <Clock className="h-6 w-6" />,
    content: [
      "Cancellations must be made at least 24 hours before your appointment",
      "Late cancellations (less than 24 hours) may incur a 50% service fee",
      "No-shows will be charged the full service amount",
      "Emergency cancellations will be handled on a case-by-case basis",
      "Repeated no-shows may result in requiring prepayment for future bookings",
    ],
  },
  {
    title: "Payment & Pricing",
    icon: <CreditCard className="h-6 w-6" />,
    content: [
      "We accept cash, credit cards, and digital payments",
      "Gratuity is appreciated but not required (15-20% is customary)",
      "Prices are subject to change with 30-day notice",
      "Package deals and memberships offer significant savings",
      "Gift certificates are available and never expire",
    ],
  },
  {
    title: "Health & Safety",
    icon: <Shield className="h-6 w-6" />,
    content: [
      "All tools are sterilized using hospital-grade equipment",
      "Single-use items are disposed of after each client",
      "Please inform us of any allergies or skin sensitivities",
      "If you have any infections or open wounds, we may need to reschedule",
      "We maintain the highest standards of cleanliness and sanitation",
    ],
  },
  {
    title: "Service Policies",
    icon: <Heart className="h-6 w-6" />,
    content: [
      "Nail art pricing varies based on complexity and time required",
      "Color changes during service may incur additional charges",
      "We guarantee our work for 48 hours after service",
      "Complimentary touch-ups within 3 days for gel services",
      "Custom designs require consultation and may need multiple sessions",
    ],
  },
  {
    title: "Important Notes",
    icon: <AlertCircle className="h-6 w-6" />,
    content: [
      "Please arrive 10 minutes early for your appointment",
      "Late arrivals may result in shortened service time or rescheduling",
      "Children under 12 must be accompanied by an adult",
      "We reserve the right to refuse service if safety standards cannot be met",
      "Photography of work may be used for marketing with client permission",
    ],
  },
]

export default function PoliciesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 to-pink-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1
            className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-pink-600 to-amber-500 bg-clip-text text-transparent"
            style={{ fontFamily: "Dancing Script, cursive" }}
          >
            Policies & Guidelines
          </h1>
          <p className="text-xl text-pink-800 max-w-3xl mx-auto">
            Please review our policies to ensure the best experience for all our clients
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {policies.map((policy, index) => (
            <Card
              key={index}
              className="border-pink-200 bg-white/80 backdrop-blur-sm hover:shadow-lg transition-shadow"
            >
              <CardHeader>
                <CardTitle className="flex items-center text-pink-800">
                  <div className="bg-gradient-to-r from-pink-500 to-amber-500 p-2 rounded-full text-white mr-3">
                    {policy.icon}
                  </div>
                  <span style={{ fontFamily: "Dancing Script, cursive" }}>{policy.title}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {policy.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start text-pink-700">
                      <div className="w-2 h-2 bg-amber-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-sm leading-relaxed">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 bg-gradient-to-r from-pink-500 to-amber-500 rounded-lg p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4" style={{ fontFamily: "Dancing Script, cursive" }}>
            Questions About Our Policies?
          </h2>
          <p className="text-lg mb-6 opacity-90">
            We're here to help! Contact us if you need clarification on any of our policies.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <p className="font-semibold">Phone</p>
              <p>(555) 123-NAIL</p>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <p className="font-semibold">Email</p>
              <p><EMAIL></p>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
              <p className="font-semibold">Hours</p>
              <p>Mon-Sat 9AM-7PM</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
