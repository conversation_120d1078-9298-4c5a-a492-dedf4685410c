"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Star, Heart, X } from "lucide-react"
import Link from "next/link"

interface ReviewPromptProps {
  appointmentId?: string
  onDismiss?: () => void
}

export function ReviewPrompt({ appointmentId, onDismiss }: ReviewPromptProps) {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  const handleDismiss = () => {
    setIsVisible(false)
    onDismiss?.()
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-end z-50 pb-20">
      <Card className="w-full mx-4 mb-4 border-0 shadow-xl">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-amber-500 rounded-full flex items-center justify-center mr-3">
                <Heart className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">How was your visit?</h3>
                <p className="text-sm text-gray-600">Your feedback helps us improve</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={handleDismiss} className="p-1">
              <X className="h-4 w-4 text-gray-500" />
            </Button>
          </div>

          <div className="flex justify-center mb-4">
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star key={star} className="h-6 w-6 text-gray-300" />
              ))}
            </div>
          </div>

          <div className="flex space-x-3">
            <Button variant="outline" className="flex-1 border-pink-200 text-pink-700" onClick={handleDismiss}>
              Maybe Later
            </Button>
            <Button
              asChild
              className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
            >
              <Link href="/review">Write Review</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
