"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Calendar, User, Settings, Sparkles, Info } from "lucide-react"
import { useAuth } from "@/components/auth/auth-provider"
import { useState } from "react"
import { GoogleAuth } from "@/components/auth/google-auth"

const baseNavItems = [
  { href: "/", icon: Home, label: "Home" },
  { href: "/services", icon: Sparkles, label: "Services" },
  { href: "/booking", icon: Calendar, label: "Book" },
  { href: "/about", icon: Info, label: "About" },
  { href: "/profile", icon: User, label: "Profile" },
]

const adminNavItem = { href: "/admin", icon: Settings, label: "Admin" }

export function BottomNav() {
  const pathname = usePathname()
  const { user, isSignedIn } = useAuth()
  const [authOpen, setAuthOpen] = useState(false)
  const [authMode, setAuthMode] = useState<"signin" | "signup">("signin")

  // Build navigation items based on user permissions
  const navItems = user?.is_admin
    ? [...baseNavItems, adminNavItem]
    : baseNavItems

  // Hide bottom nav on admin pages
  if (pathname.startsWith("/admin")) {
    return null
  }

  const handleProfileClick = (e: React.MouseEvent) => {
    if (!isSignedIn) {
      e.preventDefault()
      setAuthMode("signin")
      setAuthOpen(true)
    }
  }

  const handleAuthSuccess = () => {
    setAuthOpen(false)
  }

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-pink-200 dark:border-gray-700 safe-area-bottom z-50">
        <div className="flex justify-around items-center py-2 px-4">
          {navItems.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon
            const isProfileItem = item.href === "/profile"

            if (isProfileItem) {
              return (
                <Link
                  key={item.href}
                  href={isSignedIn ? item.href : "#"}
                  onClick={handleProfileClick}
                  className={`flex flex-col items-center py-2 px-3 rounded-lg transition-all ${
                    isActive
                      ? "text-white bg-gradient-to-r from-pink-500 to-amber-500"
                      : "text-pink-600 dark:text-pink-400 hover:text-pink-800 dark:hover:text-pink-300 hover:bg-pink-50 dark:hover:bg-gray-800"
                  }`}
                >
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs font-medium">{item.label}</span>
                </Link>
              )
            }

            // Special styling for Book button when on services page
            const isBookButton = item.href === "/booking"
            const isOnServicesPage = pathname === "/services"
            const shouldGlow = isBookButton && isOnServicesPage

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`flex flex-col items-center py-2 px-3 rounded-lg transition-all relative ${
                  isActive
                    ? "text-white bg-gradient-to-r from-pink-500 to-amber-500"
                    : shouldGlow
                    ? "text-white bg-gradient-to-r from-pink-500 to-amber-500 animate-pulse shadow-lg shadow-pink-500/50"
                    : "text-pink-600 dark:text-pink-400 hover:text-pink-800 dark:hover:text-pink-300 hover:bg-pink-50 dark:hover:bg-gray-800"
                }`}
              >
                <Icon className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">{item.label}</span>
                {shouldGlow && (
                  <div className="absolute -inset-1 bg-gradient-to-r from-pink-500 to-amber-500 rounded-lg blur opacity-30 animate-pulse" />
                )}
              </Link>
            )
          })}
        </div>
      </div>

      <GoogleAuth
        isOpen={authOpen}
        onClose={() => setAuthOpen(false)}
        mode={authMode}
        onModeChange={setAuthMode}
        onSuccess={handleAuthSuccess}
      />
    </>
  )
}
