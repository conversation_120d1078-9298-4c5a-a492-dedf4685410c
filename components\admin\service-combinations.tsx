"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Plus, Edit, Trash2, Package, Star, Clock, DollarSign } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface Service {
  id: string
  name: string
  price: number
  duration: number
  category: string
}

interface ServiceCombination {
  id: string
  name: string
  description: string
  service_ids: string[]
  discount_type: 'percentage' | 'fixed'
  discount_value: number
  original_price: number
  discounted_price: number
  total_duration: number
  featured: boolean
  active: boolean
  services?: Service[]
}

export function AdminServiceCombinations() {
  const [combinations, setCombinations] = useState<ServiceCombination[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [editingCombination, setEditingCombination] = useState<ServiceCombination | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    service_ids: [] as string[],
    discount_type: 'percentage' as 'percentage' | 'fixed',
    discount_value: 0,
    featured: false,
    active: true
  })

  useEffect(() => {
    fetchCombinations()
    fetchServices()
  }, [])

  const fetchCombinations = async () => {
    try {
      const { data, error } = await supabase
        .from('service_combinations')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching combinations:', error)
        toast.error('Failed to load service combinations')
        return
      }

      setCombinations(data || [])
    } catch (error) {
      console.error('Error fetching combinations:', error)
      toast.error('Failed to load service combinations')
    }
  }

  const fetchServices = async () => {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('active', true)
        .order('name', { ascending: true })

      if (error) {
        console.error('Error fetching services:', error)
        toast.error('Failed to load services')
        return
      }

      setServices(data || [])
    } catch (error) {
      console.error('Error fetching services:', error)
      toast.error('Failed to load services')
    } finally {
      setLoading(false)
    }
  }

  const calculatePrices = (serviceIds: string[], discountType: 'percentage' | 'fixed', discountValue: number) => {
    const selectedServices = services.filter(s => serviceIds.includes(s.id))
    const originalPrice = selectedServices.reduce((sum, service) => sum + service.price, 0)
    const totalDuration = selectedServices.reduce((sum, service) => sum + service.duration, 0)
    
    let discountedPrice = originalPrice
    if (discountType === 'percentage') {
      discountedPrice = originalPrice * (1 - discountValue / 100)
    } else {
      discountedPrice = Math.max(0, originalPrice - discountValue)
    }

    return { originalPrice, discountedPrice, totalDuration }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      service_ids: [],
      discount_type: 'percentage',
      discount_value: 0,
      featured: false,
      active: true
    })
    setEditingCombination(null)
  }

  const handleCreate = () => {
    resetForm()
    setIsCreateOpen(true)
  }

  const handleEdit = (combination: ServiceCombination) => {
    setFormData({
      name: combination.name,
      description: combination.description,
      service_ids: combination.service_ids,
      discount_type: combination.discount_type,
      discount_value: combination.discount_value,
      featured: combination.featured,
      active: combination.active
    })
    setEditingCombination(combination)
    setIsCreateOpen(true)
  }

  const handleServiceToggle = (serviceId: string, checked: boolean) => {
    if (checked) {
      setFormData({ ...formData, service_ids: [...formData.service_ids, serviceId] })
    } else {
      setFormData({ ...formData, service_ids: formData.service_ids.filter(id => id !== serviceId) })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim() || !formData.description.trim() || formData.service_ids.length < 2) {
      toast.error('Please fill in all fields and select at least 2 services')
      return
    }

    const { originalPrice, discountedPrice, totalDuration } = calculatePrices(
      formData.service_ids,
      formData.discount_type,
      formData.discount_value
    )

    try {
      const combinationData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        service_ids: formData.service_ids,
        discount_type: formData.discount_type,
        discount_value: formData.discount_value,
        original_price: originalPrice,
        discounted_price: discountedPrice,
        total_duration: totalDuration,
        featured: formData.featured,
        active: formData.active
      }

      if (editingCombination) {
        // Update existing combination
        const { error } = await supabase
          .from('service_combinations')
          .update(combinationData)
          .eq('id', editingCombination.id)

        if (error) {
          console.error('Error updating combination:', error)
          toast.error('Failed to update combination')
          return
        }

        toast.success('Combination updated successfully!')
      } else {
        // Create new combination
        const { error } = await supabase
          .from('service_combinations')
          .insert(combinationData)

        if (error) {
          console.error('Error creating combination:', error)
          toast.error('Failed to create combination')
          return
        }

        toast.success('Combination created successfully!')
      }

      setIsCreateOpen(false)
      resetForm()
      fetchCombinations()
    } catch (error) {
      console.error('Error saving combination:', error)
      toast.error('Failed to save combination')
    }
  }

  const handleDelete = async (combinationId: string) => {
    if (!confirm('Are you sure you want to delete this combination?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('service_combinations')
        .delete()
        .eq('id', combinationId)

      if (error) {
        console.error('Error deleting combination:', error)
        toast.error('Failed to delete combination')
        return
      }

      toast.success('Combination deleted successfully!')
      fetchCombinations()
    } catch (error) {
      console.error('Error deleting combination:', error)
      toast.error('Failed to delete combination')
    }
  }

  const toggleActive = async (combinationId: string, currentActive: boolean) => {
    try {
      const { error } = await supabase
        .from('service_combinations')
        .update({ active: !currentActive })
        .eq('id', combinationId)

      if (error) {
        console.error('Error toggling combination:', error)
        toast.error('Failed to update combination')
        return
      }

      toast.success(`Combination ${!currentActive ? 'activated' : 'deactivated'}`)
      fetchCombinations()
    } catch (error) {
      console.error('Error toggling combination:', error)
      toast.error('Failed to update combination')
    }
  }

  const toggleFeatured = async (combinationId: string, currentFeatured: boolean) => {
    try {
      const { error } = await supabase
        .from('service_combinations')
        .update({ featured: !currentFeatured })
        .eq('id', combinationId)

      if (error) {
        console.error('Error toggling featured:', error)
        toast.error('Failed to update combination')
        return
      }

      toast.success(`Combination ${!currentFeatured ? 'featured' : 'unfeatured'}`)
      fetchCombinations()
    } catch (error) {
      console.error('Error toggling featured:', error)
      toast.error('Failed to update combination')
    }
  }

  const { originalPrice, discountedPrice, totalDuration } = calculatePrices(
    formData.service_ids,
    formData.discount_type,
    formData.discount_value
  )

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Service Combinations</h2>
          <p className="text-gray-600">Create popular service bundles with discounts</p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleCreate} className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              <Plus className="h-4 w-4 mr-2" />
              Add Combination
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-pink-800">
                {editingCombination ? 'Edit Combination' : 'Create New Combination'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name" className="text-pink-800">Combination Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="e.g., Luxury Spa Package"
                  required
                />
              </div>
              <div>
                <Label htmlFor="description" className="text-pink-800">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="border-pink-200 focus:border-pink-500 min-h-[80px]"
                  placeholder="Describe this combination..."
                  required
                />
              </div>
              
              {/* Service Selection */}
              <div>
                <Label className="text-pink-800">Select Services (minimum 2)</Label>
                <div className="grid grid-cols-1 gap-2 mt-2 max-h-48 overflow-y-auto border border-pink-200 rounded-md p-3">
                  {services.map((service) => (
                    <div key={service.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={service.id}
                        checked={formData.service_ids.includes(service.id)}
                        onCheckedChange={(checked) => handleServiceToggle(service.id, checked as boolean)}
                      />
                      <Label htmlFor={service.id} className="flex-1 text-sm">
                        {service.name} - ${service.price} ({service.duration}min)
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Discount Configuration */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-pink-800">Discount Type</Label>
                  <Select value={formData.discount_type} onValueChange={(value: 'percentage' | 'fixed') => setFormData({ ...formData, discount_type: value })}>
                    <SelectTrigger className="border-pink-200">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">Percentage (%)</SelectItem>
                      <SelectItem value="fixed">Fixed Amount ($)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-pink-800">Discount Value</Label>
                  <Input
                    type="number"
                    min="0"
                    step={formData.discount_type === 'percentage' ? '1' : '0.01'}
                    max={formData.discount_type === 'percentage' ? '100' : undefined}
                    value={formData.discount_value}
                    onChange={(e) => setFormData({ ...formData, discount_value: parseFloat(e.target.value) || 0 })}
                    className="border-pink-200 focus:border-pink-500"
                    placeholder={formData.discount_type === 'percentage' ? '10' : '15.00'}
                  />
                </div>
              </div>

              {/* Price Preview */}
              {formData.service_ids.length > 0 && (
                <div className="bg-pink-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-pink-800 mb-2">Price Preview</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Original Price:</span>
                      <span>${originalPrice.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Discount ({formData.discount_value}{formData.discount_type === 'percentage' ? '%' : '$'}):</span>
                      <span>-${(originalPrice - discountedPrice).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-semibold text-pink-800 border-t pt-1">
                      <span>Final Price:</span>
                      <span>${discountedPrice.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-gray-600">
                      <span>Total Duration:</span>
                      <span>{Math.floor(totalDuration / 60)}h {totalDuration % 60}m</span>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => setFormData({ ...formData, featured: checked })}
                  />
                  <Label htmlFor="featured" className="text-pink-800">Featured</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="active"
                    checked={formData.active}
                    onCheckedChange={(checked) => setFormData({ ...formData, active: checked })}
                  />
                  <Label htmlFor="active" className="text-pink-800">Active</Label>
                </div>
              </div>

              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateOpen(false)}
                  className="flex-1 border-pink-300 text-pink-700"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  {editingCombination ? 'Update' : 'Create'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Combinations List */}
      <div className="space-y-4">
        {combinations.map((combination) => {
          const selectedServices = services.filter(s => combination.service_ids.includes(s.id))
          const savings = combination.original_price - combination.discounted_price
          const savingsPercentage = (savings / combination.original_price) * 100

          return (
            <Card key={combination.id} className="border-pink-200">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <CardTitle className="text-pink-800">{combination.name}</CardTitle>
                      {combination.featured && (
                        <Badge className="bg-amber-100 text-amber-800">
                          <Star className="h-3 w-3 mr-1" />
                          Featured
                        </Badge>
                      )}
                      {combination.active ? (
                        <Badge className="bg-green-100 text-green-800">Active</Badge>
                      ) : (
                        <Badge variant="outline" className="border-gray-300 text-gray-600">Inactive</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm">{combination.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-pink-800">${combination.discounted_price.toFixed(2)}</div>
                    <div className="text-sm text-gray-500 line-through">${combination.original_price.toFixed(2)}</div>
                    <div className="text-xs text-green-600">Save {savingsPercentage.toFixed(0)}%</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Included Services:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedServices.map((service) => (
                        <Badge key={service.id} variant="outline" className="border-pink-300 text-pink-700">
                          {service.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {Math.floor(combination.total_duration / 60)}h {combination.total_duration % 60}m
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 mr-1" />
                      Save ${savings.toFixed(2)}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-3 border-t">
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => toggleActive(combination.id, combination.active)}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                      >
                        {combination.active ? 'Deactivate' : 'Activate'}
                      </Button>
                      <Button
                        onClick={() => toggleFeatured(combination.id, combination.featured)}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                      >
                        {combination.featured ? 'Unfeature' : 'Feature'}
                      </Button>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        onClick={() => handleEdit(combination)}
                        variant="outline"
                        size="sm"
                        className="text-xs"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        onClick={() => handleDelete(combination.id)}
                        variant="outline"
                        size="sm"
                        className="text-xs text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {combinations.length === 0 && (
        <Card className="border-pink-200">
          <CardContent className="p-6 text-center">
            <Package className="h-12 w-12 text-pink-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 mb-2">No Combinations Created</h3>
            <p className="text-pink-600">Create your first service combination to offer bundled discounts!</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
