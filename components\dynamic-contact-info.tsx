"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { MapPin, Phone, Mail, Globe, Clock } from 'lucide-react'
import { supabase } from '@/lib/supabase'

interface BusinessInfo {
  business_name: string
  address: string
  phone: string
  email: string
  website: string | null
  description: string | null
}

interface BusinessVisibilitySettings {
  show_address: boolean
  show_phone: boolean
  show_email: boolean
  show_website: boolean
  show_hours: boolean
  show_contact_section: boolean
}

export function DynamicContactInfo() {
  const [businessInfo, setBusinessInfo] = useState<BusinessInfo | null>(null)
  const [visibilitySettings, setVisibilitySettings] = useState<BusinessVisibilitySettings | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      // Fetch business info
      const { data: businessData, error: businessError } = await supabase
        .from('business_info')
        .select('*')
        .limit(1)
        .single()

      if (businessError && businessError.code !== 'PGRST116') {
        console.error('Error fetching business info:', businessError)
      }

      // Fetch visibility settings
      const { data: visibilityData, error: visibilityError } = await supabase
        .from('business_visibility_settings')
        .select('*')
        .limit(1)
        .single()

      if (visibilityError && visibilityError.code !== 'PGRST116') {
        console.error('Error fetching visibility settings:', visibilityError)
      }

      setBusinessInfo(businessData)
      setVisibilitySettings(visibilityData || {
        show_address: true,
        show_phone: true,
        show_email: true,
        show_website: true,
        show_hours: true,
        show_contact_section: true
      })
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-50 to-amber-50 dark:from-pink-900/20 dark:to-amber-900/20 animate-pulse">
        <CardContent className="p-4">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-3 w-1/3"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Don't render if contact section is hidden
  if (!visibilitySettings?.show_contact_section) {
    return null
  }

  // Check if any contact info should be displayed
  const hasVisibleInfo = visibilitySettings.show_address || 
                         visibilitySettings.show_phone || 
                         visibilitySettings.show_email || 
                         visibilitySettings.show_website || 
                         visibilitySettings.show_hours

  if (!hasVisibleInfo) {
    return null
  }

  return (
    <Card className="border-0 shadow-sm bg-gradient-to-r from-pink-50 to-amber-50 dark:from-pink-900/20 dark:to-amber-900/20">
      <CardContent className="p-4">
        <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">Visit Us Today</h3>
        <div className="space-y-2">
          {visibilitySettings.show_address && businessInfo?.address && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <MapPin className="h-4 w-4 mr-2 text-pink-500 dark:text-pink-400" />
              {businessInfo.address}
            </div>
          )}
          
          {visibilitySettings.show_phone && businessInfo?.phone && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <Phone className="h-4 w-4 mr-2 text-pink-500 dark:text-pink-400" />
              {businessInfo.phone}
            </div>
          )}
          
          {visibilitySettings.show_email && businessInfo?.email && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <Mail className="h-4 w-4 mr-2 text-pink-500 dark:text-pink-400" />
              {businessInfo.email}
            </div>
          )}
          
          {visibilitySettings.show_website && businessInfo?.website && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <Globe className="h-4 w-4 mr-2 text-pink-500 dark:text-pink-400" />
              <a 
                href={businessInfo.website} 
                target="_blank" 
                rel="noopener noreferrer"
                className="hover:text-pink-600 dark:hover:text-pink-400 transition-colors"
              >
                {businessInfo.website.replace(/^https?:\/\//, '')}
              </a>
            </div>
          )}
          
          {visibilitySettings.show_hours && (
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
              <Clock className="h-4 w-4 mr-2 text-pink-500 dark:text-pink-400" />
              Mon-Sat 9AM-7PM
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
