"use client"

import { useState, useEffect } from 'react'
import { Calendar } from '@/components/ui/calendar'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Clock, Calendar as CalendarIcon, AlertCircle } from 'lucide-react'
import { supabase } from '@/lib/supabase'

interface TimeSlot {
  time: string
  available: boolean
  reason?: string
}

interface BookingCalendarProps {
  selectedDate?: Date
  selectedTime?: string
  totalDuration: number
  onDateSelect: (date: Date | undefined) => void
  onTimeSelect: (time: string) => void
}

export function BookingCalendar({
  selectedDate,
  selectedTime,
  totalDuration,
  onDateSelect,
  onTimeSelect
}: BookingCalendarProps) {
  const [businessHours, setBusinessHours] = useState<any[]>([])
  const [bookedSlots, setBookedSlots] = useState<Record<string, string[]>>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchBusinessHours()
    fetchBookedSlots()
  }, [])

  const fetchBusinessHours = async () => {
    try {
      const { data, error } = await supabase
        .from('business_hours')
        .select('*')
        .eq('is_open', true)
        .order('day_of_week', { ascending: true })

      if (error) {
        console.error('Error fetching business hours:', error)
        return
      }

      setBusinessHours(data || [])
    } catch (error) {
      console.error('Error fetching business hours:', error)
    }
  }

  const fetchBookedSlots = async () => {
    try {
      // In a real app, you would fetch actual bookings from the database
      // For now, we'll use mock data
      const mockBookedSlots = {
        [new Date().toISOString().split('T')[0]]: ['10:00 AM', '2:00 PM'],
        [new Date(Date.now() + 86400000).toISOString().split('T')[0]]: ['9:00 AM', '11:00 AM', '3:00 PM'],
      }
      setBookedSlots(mockBookedSlots)
    } catch (error) {
      console.error('Error fetching booked slots:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateTimeSlots = (date: Date): TimeSlot[] => {
    const dayOfWeek = date.getDay()
    const businessHour = businessHours.find(bh => bh.day_of_week === dayOfWeek)
    
    if (!businessHour) {
      return []
    }

    const slots: TimeSlot[] = []
    const startTime = parseTime(businessHour.open_time)
    const endTime = parseTime(businessHour.close_time)
    const dateKey = date.toISOString().split('T')[0]
    const bookedTimes = bookedSlots[dateKey] || []

    // Generate 30-minute slots
    for (let time = startTime; time < endTime; time += 30) {
      const timeString = formatTime(time)
      const endTimeForSlot = time + totalDuration
      
      let available = true
      let reason = ''

      // Check if slot is already booked
      if (bookedTimes.includes(timeString)) {
        available = false
        reason = 'Already booked'
      }
      // Check if appointment would end after closing time
      else if (endTimeForSlot > endTime) {
        available = false
        reason = 'Would exceed business hours'
      }
      // Check if it's too late to book for today
      else if (date.toDateString() === new Date().toDateString()) {
        const now = new Date()
        const currentTime = now.getHours() * 60 + now.getMinutes()
        if (time <= currentTime + 60) { // Need at least 1 hour notice
          available = false
          reason = 'Too short notice'
        }
      }

      slots.push({
        time: timeString,
        available,
        reason
      })
    }

    return slots
  }

  const parseTime = (timeString: string): number => {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  }

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    const period = hours >= 12 ? 'PM' : 'AM'
    const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours
    return `${displayHours}:${mins.toString().padStart(2, '0')} ${period}`
  }

  const isDateDisabled = (date: Date): boolean => {
    // Disable past dates
    if (date < new Date(new Date().setHours(0, 0, 0, 0))) {
      return true
    }

    // Check if business is open on this day
    const dayOfWeek = date.getDay()
    const businessHour = businessHours.find(bh => bh.day_of_week === dayOfWeek)
    return !businessHour || !businessHour.is_open
  }

  const timeSlots = selectedDate ? generateTimeSlots(selectedDate) : []

  if (loading) {
    return (
      <div className="grid lg:grid-cols-2 gap-8">
        <div className="h-64 bg-gray-200 rounded animate-pulse" />
        <div className="h-64 bg-gray-200 rounded animate-pulse" />
      </div>
    )
  }

  return (
    <div className="grid lg:grid-cols-2 gap-8">
      <div>
        <div className="flex items-center mb-4">
          <CalendarIcon className="h-5 w-5 text-pink-600 mr-2" />
          <h3 className="text-lg font-semibold text-pink-800">Select Date</h3>
        </div>
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={onDateSelect}
          disabled={isDateDisabled}
          className="rounded-md border border-pink-200"
        />
        
        {selectedDate && (
          <div className="mt-4 p-3 bg-pink-50 rounded-lg border border-pink-200">
            <div className="flex items-center text-sm text-pink-700">
              <Clock className="h-4 w-4 mr-1" />
              Your appointment will take {Math.floor(totalDuration / 60)}h {totalDuration % 60}m
            </div>
          </div>
        )}
      </div>

      <div>
        <div className="flex items-center mb-4">
          <Clock className="h-5 w-5 text-pink-600 mr-2" />
          <h3 className="text-lg font-semibold text-pink-800">Available Times</h3>
        </div>
        
        {selectedDate ? (
          <div className="space-y-3">
            {timeSlots.length === 0 ? (
              <Card className="border-amber-200 bg-amber-50">
                <CardContent className="p-4 text-center">
                  <AlertCircle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
                  <p className="text-amber-700">We're closed on this day</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-2 gap-3 max-h-80 overflow-y-auto">
                {timeSlots.map((slot) => (
                  <Button
                    key={slot.time}
                    variant={selectedTime === slot.time ? "default" : "outline"}
                    disabled={!slot.available}
                    onClick={() => onTimeSelect(slot.time)}
                    className={`h-12 ${
                      selectedTime === slot.time
                        ? "bg-gradient-to-r from-pink-500 to-amber-500 text-white"
                        : slot.available
                        ? "border-pink-200 text-pink-700 hover:bg-pink-50"
                        : "opacity-50 cursor-not-allowed"
                    }`}
                    title={!slot.available ? slot.reason : undefined}
                  >
                    <div className="text-center">
                      <div className="font-semibold">{slot.time}</div>
                      {!slot.available && (
                        <div className="text-xs opacity-70">Unavailable</div>
                      )}
                    </div>
                  </Button>
                ))}
              </div>
            )}
            
            {timeSlots.some(slot => !slot.available) && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Legend:</h4>
                <div className="space-y-1 text-xs text-gray-600">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-pink-500 rounded mr-2"></div>
                    Available
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-gray-300 rounded mr-2"></div>
                    Unavailable
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <Card className="border-pink-200">
            <CardContent className="p-6 text-center">
              <CalendarIcon className="h-12 w-12 text-pink-300 mx-auto mb-4" />
              <p className="text-pink-600">Please select a date first</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
