"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Star, MessageSquare, Calendar } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

interface CustomerReview {
  id: string
  customer_name: string
  rating: number
  review_text: string
  service_name: string | null
  review_date: string
  featured: boolean
  approved: boolean
}

export function AdminCustomerReviews() {
  const [reviews, setReviews] = useState<CustomerReview[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateOpen, setIsCreateOpen] = useState(false)
  const [editingReview, setEditingReview] = useState<CustomerReview | null>(null)
  const [formData, setFormData] = useState({
    customer_name: '',
    rating: 5,
    review_text: '',
    service_name: '',
    review_date: new Date().toISOString().split('T')[0],
    featured: false,
    approved: true
  })

  useEffect(() => {
    fetchReviews()
  }, [])

  const fetchReviews = async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('customer_reviews')
        .select('*')
        .order('review_date', { ascending: false })

      if (error) {
        console.error('Error fetching reviews:', error)
        toast.error('Failed to load reviews')
        return
      }

      setReviews(data || [])
    } catch (error) {
      console.error('Error fetching reviews:', error)
      toast.error('Failed to load reviews')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      customer_name: '',
      rating: 5,
      review_text: '',
      service_name: '',
      review_date: new Date().toISOString().split('T')[0],
      featured: false,
      approved: true
    })
    setEditingReview(null)
  }

  const handleCreate = () => {
    resetForm()
    setIsCreateOpen(true)
  }

  const handleEdit = (review: CustomerReview) => {
    setFormData({
      customer_name: review.customer_name,
      rating: review.rating,
      review_text: review.review_text,
      service_name: review.service_name || '',
      review_date: review.review_date,
      featured: review.featured,
      approved: review.approved
    })
    setEditingReview(review)
    setIsCreateOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.customer_name.trim() || !formData.review_text.trim()) {
      toast.error('Please fill in all required fields')
      return
    }

    try {
      const reviewData = {
        customer_name: formData.customer_name.trim(),
        rating: formData.rating,
        review_text: formData.review_text.trim(),
        service_name: formData.service_name.trim() || null,
        review_date: formData.review_date,
        featured: formData.featured,
        approved: formData.approved
      }

      if (editingReview) {
        // Update existing review
        const { error } = await supabase
          .from('customer_reviews')
          .update(reviewData)
          .eq('id', editingReview.id)

        if (error) {
          console.error('Error updating review:', error)
          toast.error('Failed to update review')
          return
        }

        toast.success('Review updated successfully!')
      } else {
        // Create new review
        const { error } = await supabase
          .from('customer_reviews')
          .insert(reviewData)

        if (error) {
          console.error('Error creating review:', error)
          toast.error('Failed to create review')
          return
        }

        toast.success('Review created successfully!')
      }

      setIsCreateOpen(false)
      resetForm()
      fetchReviews()
    } catch (error) {
      console.error('Error saving review:', error)
      toast.error('Failed to save review')
    }
  }

  const handleDelete = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('customer_reviews')
        .delete()
        .eq('id', reviewId)

      if (error) {
        console.error('Error deleting review:', error)
        toast.error('Failed to delete review')
        return
      }

      toast.success('Review deleted successfully!')
      fetchReviews()
    } catch (error) {
      console.error('Error deleting review:', error)
      toast.error('Failed to delete review')
    }
  }

  const toggleFeatured = async (reviewId: string, currentFeatured: boolean) => {
    try {
      const { error } = await supabase
        .from('customer_reviews')
        .update({ featured: !currentFeatured })
        .eq('id', reviewId)

      if (error) {
        console.error('Error toggling featured:', error)
        toast.error('Failed to update review')
        return
      }

      toast.success(`Review ${!currentFeatured ? 'featured' : 'unfeatured'}`)
      fetchReviews()
    } catch (error) {
      console.error('Error toggling featured:', error)
      toast.error('Failed to update review')
    }
  }

  const toggleApproved = async (reviewId: string, currentApproved: boolean) => {
    try {
      const { error } = await supabase
        .from('customer_reviews')
        .update({ approved: !currentApproved })
        .eq('id', reviewId)

      if (error) {
        console.error('Error toggling approved:', error)
        toast.error('Failed to update review')
        return
      }

      toast.success(`Review ${!currentApproved ? 'approved' : 'unapproved'}`)
      fetchReviews()
    } catch (error) {
      console.error('Error toggling approved:', error)
      toast.error('Failed to update review')
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-amber-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Customer Reviews</h2>
          <p className="text-gray-600">Manage customer reviews and testimonials</p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleCreate} className="bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600">
              <Plus className="h-4 w-4 mr-2" />
              Add Review
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white max-w-md max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-pink-800">
                {editingReview ? 'Edit Review' : 'Create New Review'}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="customer_name" className="text-pink-800">Customer Name</Label>
                <Input
                  id="customer_name"
                  value={formData.customer_name}
                  onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="Customer's name"
                  required
                />
              </div>
              <div>
                <Label htmlFor="rating" className="text-pink-800">Rating</Label>
                <div className="flex items-center space-x-2 mt-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setFormData({ ...formData, rating: star })}
                      className="focus:outline-none"
                    >
                      <Star
                        className={`h-6 w-6 ${star <= formData.rating ? 'text-amber-400 fill-current' : 'text-gray-300'}`}
                      />
                    </button>
                  ))}
                  <span className="text-sm text-gray-600 ml-2">{formData.rating} star{formData.rating !== 1 ? 's' : ''}</span>
                </div>
              </div>
              <div>
                <Label htmlFor="review_text" className="text-pink-800">Review Text</Label>
                <Textarea
                  id="review_text"
                  value={formData.review_text}
                  onChange={(e) => setFormData({ ...formData, review_text: e.target.value })}
                  className="border-pink-200 focus:border-pink-500 min-h-[100px]"
                  placeholder="Customer's review..."
                  required
                />
              </div>
              <div>
                <Label htmlFor="service_name" className="text-pink-800">Service Name (Optional)</Label>
                <Input
                  id="service_name"
                  value={formData.service_name}
                  onChange={(e) => setFormData({ ...formData, service_name: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  placeholder="e.g., Gel Manicure"
                />
              </div>
              <div>
                <Label htmlFor="review_date" className="text-pink-800">Review Date</Label>
                <Input
                  id="review_date"
                  type="date"
                  value={formData.review_date}
                  onChange={(e) => setFormData({ ...formData, review_date: e.target.value })}
                  className="border-pink-200 focus:border-pink-500"
                  required
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => setFormData({ ...formData, featured: checked })}
                  />
                  <Label htmlFor="featured" className="text-pink-800">Featured</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="approved"
                    checked={formData.approved}
                    onCheckedChange={(checked) => setFormData({ ...formData, approved: checked })}
                  />
                  <Label htmlFor="approved" className="text-pink-800">Approved</Label>
                </div>
              </div>
              <div className="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateOpen(false)}
                  className="flex-1 border-pink-300 text-pink-700"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-pink-500 to-amber-500 hover:from-pink-600 hover:to-amber-600"
                >
                  {editingReview ? 'Update' : 'Create'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id} className="border-pink-200">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <CardTitle className="text-pink-800">{review.customer_name}</CardTitle>
                    <div className="flex items-center space-x-1">
                      {renderStars(review.rating)}
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(review.review_date).toLocaleDateString()}
                    </div>
                    {review.service_name && (
                      <div>Service: {review.service_name}</div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {review.featured && (
                    <Badge className="bg-amber-100 text-amber-800">Featured</Badge>
                  )}
                  {review.approved ? (
                    <Badge className="bg-green-100 text-green-800">Approved</Badge>
                  ) : (
                    <Badge variant="outline" className="border-red-300 text-red-600">Pending</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-4">{review.review_text}</p>
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <Button
                    onClick={() => toggleFeatured(review.id, review.featured)}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                  >
                    {review.featured ? 'Unfeature' : 'Feature'}
                  </Button>
                  <Button
                    onClick={() => toggleApproved(review.id, review.approved)}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                  >
                    {review.approved ? 'Unapprove' : 'Approve'}
                  </Button>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={() => handleEdit(review)}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                  <Button
                    onClick={() => handleDelete(review.id)}
                    variant="outline"
                    size="sm"
                    className="text-xs text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {reviews.length === 0 && (
        <Card className="border-pink-200">
          <CardContent className="p-6 text-center">
            <MessageSquare className="h-12 w-12 text-pink-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-pink-800 mb-2">No Reviews Yet</h3>
            <p className="text-pink-600">Create your first customer review to get started!</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
